server {
    listen       80;
    server_name  cn-iop-web-dev.gac-international.com;
    gzip on;
    gzip_buffers 32 4K;
    gzip_comp_level 6;
    gzip_min_length 100;
    gzip_types application/javascript text/css text/xml;
    gzip_disable "MSIE [1-6]\.";
    gzip_vary on;
    # 设置单个请求头缓冲区大小
    client_header_buffer_size 16k;

    # 设置大型请求头缓冲区大小和数量
    large_client_header_buffers 4 32k;

    underscores_in_headers on;  # 允许接收带下划线的头

    add_header Cache-Control "public, max-age=31536000";

    # 主应用处理所有路径（强制返回主应用的 index.html）
    location / {
        root /usr/share/nginx/html/;
        try_files $uri $uri/ /index.html;  # 所有路径回退到主应用入口
    }

    # 子应用静态资源（精确匹配路径）
    location ^~ /gac_iop_childapp/ {
        alias /usr/share/nginx/html/childapp/;
        try_files $uri $uri/ /index.html;
        add_header Access-Control-Allow-Origin *;
    }

    # 子应用接口代理
    location ^~ /gac_iop_childapp/manage/ {
        proxy_pass http://**************:8080/manage/;
        proxy_set_header Host $proxy_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        # 关键配置：透传 B_accesstoken（注意变量名格式）
        proxy_set_header B_accesstoken $http_b_accesstoken; 
        client_max_body_size 50M;
    }

    # 主应用接口代理-走网关
    location /manage/ {
        proxy_pass http://**************:8080;
        proxy_set_header Host $proxy_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        # 关键配置：透传 B_accesstoken（注意变量名格式）
        proxy_set_header B_accesstoken $http_b_accesstoken;
    }

    # 主应用统计接口代理-走网关
    location /data/statistics/ {
        proxy_pass http://**************:8080;
        proxy_set_header Host $proxy_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme; 
        # 关键配置：透传 B_accesstoken（注意变量名格式）
        proxy_set_header B_accesstoken $http_b_accesstoken;
    }

}
