{"name": "gq-unified-platform", "version": "1.0.0", "scripts": {"pinstall": "pnpm install", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "dev": "vite", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build && esno ./build/script/postBuild.ts", "build:analyze": "node scripts/build-analysis.js", "build:fast": "cross-env NODE_ENV=production NODE_OPTIONS=\"--max-old-space-size=8192 --max-semi-space-size=512\" vite build && esno ./build/script/postBuild.ts", "build:bra": "cross-env NODE_ENV=bra NODE_OPTIONS=--max-old-space-size=8192 vite build --mode bra && esno ./build/script/postBuild.ts", "build:me": "cross-env NODE_ENV=me NODE_OPTIONS=--max-old-space-size=8192 vite build --mode me && esno ./build/script/postBuild.ts", "build:eu": "cross-env NODE_ENV=eu NODE_OPTIONS=--max-old-space-size=8192 vite build --mode eu && esno ./build/script/postBuild.ts", "build:rus": "cross-env NODE_ENV=rus NODE_OPTIONS=--max-old-space-size=8192 vite build --mode rus && esno ./build/script/postBuild.ts", "build:sea": "cross-env NODE_ENV=sea NODE_OPTIONS=--max-old-space-size=8192 vite build --mode sea && esno ./build/script/postBuild.ts", "build:bra_uat": "cross-env NODE_ENV=bra_uat NODE_OPTIONS=--max-old-space-size=8192 vite build --mode bra_uat && esno ./build/script/postBuild.ts", "build:me_uat": "cross-env NODE_ENV=me_uat NODE_OPTIONS=--max-old-space-size=8192 vite build --mode me_uat && esno ./build/script/postBuild.ts", "build:eu_uat": "cross-env NODE_ENV=eu_uat NODE_OPTIONS=--max-old-space-size=8192 vite build --mode eu_uat && esno ./build/script/postBuild.ts", "build:rus_uat": "cross-env NODE_ENV=rus_uat NODE_OPTIONS=--max-old-space-size=8192 vite build --mode rus_uat && esno ./build/script/postBuild.ts", "build:sea_uat": "cross-env NODE_ENV=sea_uat NODE_OPTIONS=--max-old-space-size=8192 vite build --mode sea_uat && esno ./build/script/postBuild.ts", "build:test": "cross-env NODE_ENV=sandbox NODE_OPTIONS=--max-old-space-size=8192 vite build --mode sandbox && esno ./build/script/postBuild.ts", "build:uat": "cross-env NODE_ENV=uat NODE_OPTIONS=--max-old-space-size=8192 vite build --mode uat && esno ./build/script/postBuild.ts", "build:report": "pnpm clean:cache && cross-env REPORT=true npm run build", "preview": "npm run build && vite preview", "reinstall": "rimraf pnpm-lock.yaml && rimraf yarn.lock && rimraf package.lock.json && rimraf node_modules && npm run install", "clean:lib": "rimraf node_modules", "gen:icon": "esno ./build/generate/icon/index.ts", "batch:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "upgrade:log": "conventional-changelog -p angular -i CHANGELOG.md -s", "husky:install": "husky install", "analyze:bundle": "pnpm build:report && npx webpack-bundle-analyzer dist/stats.json", "analyze:vite": "npx vite-bundle-analyzer", "analyze:deps": "npm-check", "analyze:size": "size-limit", "analyze:deps-unused": "depcheck", "analyze:deps-size": "node scripts/analyze-dependencies.js"}, "dependencies": {"@ant-design/colors": "^7.2.0", "@ant-design/icons-vue": "^7.0.1", "@jeecg/online": "3.7.1-<PERSON>", "@ruqi/utils-admin": "^1.2.0", "@tinymce/tinymce-vue": "4.0.7", "@traptitech/markdown-it-katex": "^3.6.0", "@vant/area-data": "^1.5.2", "@vue/shared": "^3.5.13", "@vueuse/core": "^10.11.1", "@zxcvbn-ts/core": "^3.0.4", "ant-design-vue": "^4.2.6", "axios": "^1.7.9", "clipboard": "^2.0.11", "cropperjs": "^1.6.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dom-align": "^1.12.4", "echarts": "^5.6.0", "emoji-mart-vue-fast": "^15.0.3", "event-source-polyfill": "^1.0.31", "highlight.js": "^11.11.1", "intro.js": "^7.2.0", "jsencrypt": "^3.3.2", "iconify-icon": "^3.0.0", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "markdown-it-link-attributes": "^4.0.1", "nprogress": "^0.2.0", "path-to-regexp": "^6.3.0", "pinia": "2.1.7", "qiankun": "^2.10.16", "qs": "^6.13.1", "resize-observer-polyfill": "^1.5.1", "showdown": "^2.1.0", "sortablejs": "^1.15.6", "tinymce": "6.6.2", "vant": "^4.9.20", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-hooks-plus": "^2.3.0", "vue-i18n": "^9.14.2", "vue-router": "^4.5.0", "vue-types": "^5.1.3", "vxe-table": "4.6.17", "vxe-table-plugin-antd": "4.0.7", "xe-utils": "3.5.26", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@rys-fe/vite-plugin-theme": "^0.8.6", "@size-limit/preset-app": "^11.2.0", "@swc/core": "^1.12.14", "@types/crypto-js": "^4.2.2", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/lodash-es": "^4.17.12", "@types/node": "^20.17.12", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.17", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/compiler-sfc": "^3.5.13", "autoprefixer": "^10.4.20", "big.js": "^6.2.2", "commitizen": "^4.3.1", "conventional-changelog-cli": "^4.1.0", "cross-env": "^7.0.3", "cz-git": "^1.11.0", "czg": "^1.11.0", "dotenv": "^16.4.7", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.32.0", "esno": "^4.8.0", "fs-extra": "^11.2.0", "inquirer": "^9.3.7", "less": "^4.2.1", "picocolors": "^1.1.1", "postcss": "^8.4.49", "postcss-html": "^1.7.0", "postcss-less": "^6.0.0", "postcss-px-2-vp-pro": "^0.0.5", "prettier": "^3.4.2", "rollup": "^4.30.0", "rollup-plugin-visualizer": "^5.13.1", "stylelint": "^16.12.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recommended": "^14.0.1", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.1", "stylelint-order": "^6.0.4", "ts-node": "^10.9.2", "typescript": "^4.9.5", "unocss": "^0.58.9", "vite": "^6.0.7", "vite-bundle-analyzer": "^1.1.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-mkcert": "^1.17.6", "vite-plugin-mock": "^2.9.8", "vite-plugin-qiankun": "^1.0.15", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^1.8.27"}, "license": "MIT", "engines": {"node": "^18 || >=20"}}