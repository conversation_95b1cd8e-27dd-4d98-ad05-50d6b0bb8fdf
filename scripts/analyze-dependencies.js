#!/usr/bin/env node

/**
 * 依赖分析脚本
 * 分析项目中体积较大的依赖包及其使用情况
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 开始分析项目依赖...\n');

// 读取 package.json
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };

// 获取包大小信息
function getPackageSize(packageName) {
  try {
    const result = execSync(`npm view ${packageName} dist.unpackedSize`, { encoding: 'utf8' });
    return parseInt(result.trim()) || 0;
  } catch (error) {
    return 0;
  }
}

// 分析依赖大小
console.log('📦 分析依赖包大小...');
const packageSizes = [];

for (const [name, version] of Object.entries(dependencies)) {
  const size = getPackageSize(name);
  if (size > 0) {
    packageSizes.push({
      name,
      version,
      size,
      sizeFormatted: formatBytes(size)
    });
  }
}

// 按大小排序
packageSizes.sort((a, b) => b.size - a.size);

// 格式化字节数
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 输出结果
console.log('\n📊 依赖包大小排行榜 (Top 20):');
console.log('=' * 60);
packageSizes.slice(0, 20).forEach((pkg, index) => {
  console.log(`${(index + 1).toString().padStart(2)}. ${pkg.name.padEnd(30)} ${pkg.sizeFormatted.padStart(10)} (${pkg.version})`);
});

// 重点关注的大型依赖
const largePackages = packageSizes.filter(pkg => pkg.size > 1024 * 1024); // > 1MB
if (largePackages.length > 0) {
  console.log('\n🚨 体积超过 1MB 的依赖包:');
  console.log('=' * 60);
  largePackages.forEach(pkg => {
    console.log(`- ${pkg.name}: ${pkg.sizeFormatted}`);
  });
}

// 建议优化的依赖
console.log('\n💡 建议重点关注的依赖包:');
console.log('=' * 60);

const suggestions = [
  {
    name: 'echarts',
    suggestion: '考虑按需导入图表类型，或使用 echarts/core + 按需导入'
  },
  {
    name: 'ant-design-vue',
    suggestion: '确认是否已启用按需导入，检查是否有未使用的组件'
  },
  {
    name: 'tinymce',
    suggestion: '如果同时使用了 vditor，考虑只保留一个富文本编辑器'
  },
  {
    name: 'vditor',
    suggestion: '如果同时使用了 tinymce，考虑只保留一个富文本编辑器'
  },
  {
    name: 'mammoth',
    suggestion: '检查 Word 文档处理功能是否必需'
  },
  {
    name: 'pdfjs-dist',
    suggestion: '检查 PDF 处理功能是否必需'
  },
  {
    name: 'exceljs',
    suggestion: '如果同时使用了 xlsx，考虑统一使用一个 Excel 处理库'
  },
  {
    name: 'xlsx',
    suggestion: '如果同时使用了 exceljs，考虑统一使用一个 Excel 处理库'
  },
  {
    name: 'qiankun',
    suggestion: '检查微前端功能是否必需'
  },
  {
    name: 'highlight.js',
    suggestion: '检查代码高亮功能是否必需'
  },
  {
    name: 'intro.js',
    suggestion: '检查引导功能是否必需'
  },
  {
    name: 'vue-print-nb-jeecg',
    suggestion: '检查打印功能是否必需'
  }
];

suggestions.forEach(({ name, suggestion }) => {
  const pkg = packageSizes.find(p => p.name === name);
  if (pkg) {
    console.log(`- ${name} (${pkg.sizeFormatted}): ${suggestion}`);
  }
});

console.log('\n🔧 下一步操作建议:');
console.log('=' * 60);
console.log('1. 运行 `pnpm analyze:deps` 检查过时和未使用的依赖');
console.log('2. 运行 `pnpm analyze:deps-unused` 检查未使用的依赖');
console.log('3. 运行 `pnpm build:report` 生成构建报告');
console.log('4. 运行 `pnpm analyze:bundle` 分析打包后的文件大小');
console.log('5. 根据业务需求，逐个评估上述建议中的依赖包');

console.log('\n✅ 依赖分析完成!');
