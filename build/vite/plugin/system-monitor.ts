import { cpus, totalmem, freemem } from 'os';
import type { Plugin } from 'vite';

interface SystemMetrics {
  timestamp: number;
  memoryUsage: NodeJS.MemoryUsage;
  freeMemory: number;
  cpuCount: number;
}

export function systemMonitorPlugin(): Plugin {
  let startTime: number;
  let startMemory: NodeJS.MemoryUsage;
  let peakMemory = 0;
  let peakHeapUsed = 0;
  const metrics: SystemMetrics[] = [];
  let monitorInterval: NodeJS.Timeout | null = null;

  const formatBytes = (bytes: number): string => {
    return `${(bytes / 1024 / 1024).toFixed(2)} MB`;
  };

  const formatGB = (bytes: number): string => {
    return `${(bytes / 1024 / 1024 / 1024).toFixed(2)} GB`;
  };

  const collectMetrics = () => {
    const memUsage = process.memoryUsage();
    peakMemory = Math.max(peakMemory, memUsage.rss);
    peakHeapUsed = Math.max(peakHeapUsed, memUsage.heapUsed);
    
    metrics.push({
      timestamp: Date.now(),
      memoryUsage: memUsage,
      freeMemory: freemem(),
      cpuCount: cpus().length
    });
  };

  return {
    name: 'system-monitor',
    
    buildStart() {
      startTime = Date.now();
      startMemory = process.memoryUsage();
      peakMemory = startMemory.rss;
      peakHeapUsed = startMemory.heapUsed;
      
      console.log('\n💻 系统信息:');
      console.log(`CPU 核心数: ${cpus().length}`);
      console.log(`总内存: ${formatGB(totalmem())}`);
      console.log(`可用内存: ${formatGB(freemem())}`);
      console.log(`Node.js 版本: ${process.version}`);
      
      // 每秒收集一次指标
      monitorInterval = setInterval(collectMetrics, 1000);
      collectMetrics(); // 立即收集一次
    },
    
    buildEnd() {
      if (monitorInterval) {
        clearInterval(monitorInterval);
        monitorInterval = null;
      }
      
      collectMetrics(); // 最后收集一次
      
      const endTime = Date.now();
      const endMemory = process.memoryUsage();
      const buildDuration = endTime - startTime;
      
      console.log('\n📊 系统资源使用分析:');
      
      // 内存使用分析
      console.log('\n🧠 内存使用情况:');
      console.table({
        '开始时': {
          'RSS': formatBytes(startMemory.rss),
          'Heap Used': formatBytes(startMemory.heapUsed),
          'Heap Total': formatBytes(startMemory.heapTotal),
          'External': formatBytes(startMemory.external)
        },
        '结束时': {
          'RSS': formatBytes(endMemory.rss),
          'Heap Used': formatBytes(endMemory.heapUsed),
          'Heap Total': formatBytes(endMemory.heapTotal),
          'External': formatBytes(endMemory.external)
        },
        '峰值': {
          'RSS': formatBytes(peakMemory),
          'Heap Used': formatBytes(peakHeapUsed),
          'Heap Total': 'N/A',
          'External': 'N/A'
        }
      });
      
      // 内存增长分析
      const memoryGrowth = endMemory.heapUsed - startMemory.heapUsed;
      const memoryGrowthMB = memoryGrowth / 1024 / 1024;
      
      console.log(`\n📈 内存使用变化:`);
      console.log(`Heap 内存增长: ${memoryGrowthMB > 0 ? '+' : ''}${memoryGrowthMB.toFixed(2)} MB`);
      console.log(`峰值内存使用: ${formatBytes(peakHeapUsed)}`);
      
      // 性能建议
      console.log('\n💡 性能建议:');
      
      if (peakHeapUsed > 1024 * 1024 * 1024) { // > 1GB
        console.log('⚠️ 内存使用较高 (>1GB)，建议检查是否有内存泄漏');
      } else if (peakHeapUsed > 512 * 1024 * 1024) { // > 512MB
        console.log('⚠️ 内存使用中等 (>512MB)，可以考虑优化大型依赖');
      } else {
        console.log('✅ 内存使用正常');
      }
      
      if (buildDuration > 30000) { // > 30s
        console.log('⚠️ 构建时间较长，建议启用并行构建或优化依赖');
      } else if (buildDuration > 15000) { // > 15s
        console.log('⚠️ 构建时间中等，可以考虑进一步优化');
      } else {
        console.log('✅ 构建时间良好');
      }
      
      // CPU 使用建议
      const cpuCount = cpus().length;
      if (cpuCount >= 8) {
        console.log('✅ CPU 核心充足，可以考虑启用更多并行处理');
      } else if (cpuCount >= 4) {
        console.log('✅ CPU 核心适中，当前配置应该足够');
      } else {
        console.log('⚠️ CPU 核心较少，构建性能可能受限');
      }
      
      // 系统可用内存检查
      const freeMemGB = freemem() / 1024 / 1024 / 1024;
      if (freeMemGB < 1) {
        console.log('⚠️ 系统可用内存不足 (<1GB)，可能影响构建性能');
      } else if (freeMemGB < 2) {
        console.log('⚠️ 系统可用内存较少 (<2GB)，建议关闭其他应用');
      } else {
        console.log('✅ 系统可用内存充足');
      }
    }
  };
}
