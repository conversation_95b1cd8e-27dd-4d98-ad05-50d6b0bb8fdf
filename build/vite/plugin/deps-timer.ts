import type { Plugin } from 'vite';

interface DependencyTiming {
  name: string;
  loadTime: number;
  size?: number;
  firstLoad: number;
}

export function depsTimerPlugin(): Plugin {
  const depsTiming = new Map<string, DependencyTiming>();
  const loadStartTimes = new Map<string, number>();

  const extractDepName = (id: string): string | null => {
    const nodeModulesIndex = id.indexOf('node_modules/');
    if (nodeModulesIndex === -1) return null;
    
    const afterNodeModules = id.substring(nodeModulesIndex + 13);
    const parts = afterNodeModules.split('/');
    
    // 处理 scoped packages (@xxx/yyy)
    if (parts[0].startsWith('@')) {
      return parts.length > 1 ? `${parts[0]}/${parts[1]}` : parts[0];
    }
    
    return parts[0];
  };

  return {
    name: 'deps-timer',
    
    configureServer(server) {
      // 提供依赖时间分析的 API 端点
      server.middlewares.use('/api/deps-timing', (req, res) => {
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Access-Control-Allow-Origin', '*');
        
        const depsArray = Array.from(depsTiming.entries())
          .map(([name, timing]) => ({
            name,
            loadTime: timing.loadTime,
            size: timing.size,
            firstLoad: timing.firstLoad
          }))
          .sort((a, b) => b.loadTime - a.loadTime);
        
        res.end(JSON.stringify({
          dependencies: depsArray,
          totalDeps: depsArray.length,
          totalLoadTime: depsArray.reduce((sum, dep) => sum + dep.loadTime, 0)
        }, null, 2));
      });
    },

    load(id) {
      const depName = extractDepName(id);
      if (!depName) return;
      
      const startTime = Date.now();
      loadStartTimes.set(id, startTime);
      
      // 记录首次加载时间
      if (!depsTiming.has(depName)) {
        depsTiming.set(depName, {
          name: depName,
          loadTime: 0,
          firstLoad: startTime
        });
      }
    },

    transform(code, id) {
      const depName = extractDepName(id);
      if (!depName) return;
      
      const startTime = loadStartTimes.get(id);
      if (startTime) {
        const loadTime = Date.now() - startTime;
        const existing = depsTiming.get(depName);
        
        if (existing) {
          existing.loadTime += loadTime;
          existing.size = (existing.size || 0) + code.length;
        }
        
        loadStartTimes.delete(id);
      }
    },

    buildEnd() {
      const sortedDeps = Array.from(depsTiming.entries())
        .map(([name, timing]) => ({ name, ...timing }))
        .sort((a, b) => b.loadTime - a.loadTime)
        .slice(0, 10);

      if (sortedDeps.length > 0) {
        console.log('\n📦 依赖加载时间分析 (Top 10):');
        console.table(sortedDeps.map(dep => ({
          依赖名称: dep.name,
          加载时间: `${dep.loadTime}ms`,
          代码大小: dep.size ? `${(dep.size / 1024).toFixed(1)}KB` : 'N/A'
        })));

        const totalLoadTime = sortedDeps.reduce((sum, dep) => sum + dep.loadTime, 0);
        console.log(`📊 Top 10 依赖总加载时间: ${totalLoadTime}ms`);
        
        // 性能建议
        const slowDeps = sortedDeps.filter(dep => dep.loadTime > 1000);
        if (slowDeps.length > 0) {
          console.log('\n⚠️ 加载较慢的依赖 (>1s):');
          slowDeps.forEach(dep => {
            console.log(`  - ${dep.name}: ${dep.loadTime}ms`);
          });
          console.log('💡 建议: 考虑将这些依赖添加到 optimizeDeps.include 中进行预构建');
        }
      }
    }
  };
}
