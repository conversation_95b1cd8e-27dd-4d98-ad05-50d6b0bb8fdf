import { Plugin } from 'vite'
import { transform } from '@swc/core'

/**
 * SWC 压缩插件 - 专门用于压缩 JavaScript 代码
 * 采用"平衡优化"策略：在保证运行时安全的前提下获得良好的压缩效果
 *
 * 配置原则：
 * 1. 启用经过验证的安全优化选项
 * 2. 禁用可能导致运行时异常的危险优化（如属性名混淆）
 * 3. 保持函数名和类名，避免框架反射问题
 * 4. 谨慎处理副作用移除，避免破坏框架逻辑
 * 5. 目标：比 esbuild 压缩效果提升 10%+ 且运行稳定
 */
export function createSwcMinifyPlugin(): Plugin {
  return {
    name: 'vite:swc-minify',
    apply: 'build',
    enforce: 'post',
    
    async generateBundle(_, bundle) {

      console.log('🚀 使用 SWC 进行代码压缩...')
      
      const startTime = Date.now()
      let processedFiles = 0
      
      // 对所有 JS 文件使用 SWC 进行压缩
      for (const [fileName, chunk] of Object.entries(bundle)) {
        if (chunk.type === 'chunk' && fileName.endsWith('.js')) {
          try {
            const result = await transform(chunk.code, {
              jsc: {
                target: 'es2018',  // 保守的目标版本，确保兼容性
                parser: {
                  syntax: 'ecmascript',
                  jsx: false,
                  dynamicImport: true,
                  privateMethod: true,
                  functionBind: false,
                  exportDefaultFrom: true,
                  exportNamespaceFrom: true,
                  decorators: false,
                  decoratorsBeforeExport: true,
                  topLevelAwait: true,
                  importMeta: true,
                },
                minify: {
                  compress: {
                    // === 启用的安全优化选项 ===
                    arguments: false,           // 不优化 arguments，避免函数调用问题
                    arrows: true,              // 箭头函数优化（安全）
                    booleans: true,            // 布尔值优化（安全）
                    booleans_as_integers: false, // 不将布尔值转为整数
                    collapse_vars: true,        // 启用变量合并（相对安全）
                    comparisons: true,         // 比较操作优化（安全）
                    computed_props: true,      // 启用计算属性优化（谨慎使用）
                    conditionals: true,        // 条件语句优化（安全）
                    dead_code: true,           // 移除死代码（安全）
                    directives: true,          // 指令优化（安全）
                    drop_console: true,        // 移除 console（生产环境安全）
                    drop_debugger: true,       // 移除 debugger（生产环境安全）
                    evaluate: true,            // 启用常量求值（相对安全）
                    expression: false,         // 禁用表达式优化（避免框架问题）
                    hoist_funs: false,         // 不提升函数，保持原有作用域
                    hoist_props: false,        // 不提升属性，避免对象结构变化
                    hoist_vars: false,         // 不提升变量，保持原有作用域
                    if_return: true,           // 启用 if-return 优化（相对安全）
                    join_vars: true,           // 启用变量声明合并（相对安全）
                    keep_classnames: true,     // 保持类名，避免反射问题
                    keep_fargs: true,          // 保持函数参数名
                    keep_fnames: true,         // 保持函数名，避免框架问题
                    keep_infinity: true,       // 保持 Infinity
                    loops: true,               // 启用循环优化（相对安全）
                    negate_iife: true,         // 启用 IIFE 优化（相对安全）
                    properties: false,         // 禁用属性访问优化（避免框架问题）
                    reduce_funcs: false,       // 不简化函数，保持原有逻辑
                    reduce_vars: false,        // 禁用变量简化（避免作用域问题）
                    side_effects: false,       // 禁用副作用移除（避免框架逻辑问题）
                    switches: true,            // switch 语句优化（相对安全）
                    typeofs: true,             // typeof 优化（安全）
                    unsafe: false,             // 禁用所有不安全优化
                    unsafe_arrows: false,      // 禁用不安全的箭头函数优化
                    unsafe_comps: false,       // 禁用不安全的比较优化
                    // @ts-ignore
                    unsafe_Function: false,    // 禁用不安全的 Function 优化
                    unsafe_math: false,        // 禁用不安全的数学优化
                    unsafe_symbols: false,     // 禁用不安全的 Symbol 优化
                    unsafe_methods: false,     // 禁用不安全的方法优化
                    unsafe_proto: false,       // 禁用不安全的原型优化
                    unsafe_regexp: false,      // 禁用不安全的正则优化
                    unsafe_undefined: false,   // 禁用不安全的 undefined 优化
                    unused: true,              // 启用移除未使用代码（相对安全）
                  },
                  mangle: {
                    // 保守的变量名混淆配置
                    keep_classnames: true,     // 保持类名，避免反射问题
                    keep_fnames: true,         // 保持函数名，避免框架问题
                    reserved: [                // 扩展的保留变量名列表
                      // 核心环境变量
                      'exports', 'require', 'module', 'global', 'window', 'document', 'console',
                      'process', '__dirname', '__filename', 'Buffer',
                      // Vue 生态关键变量
                      'Vue', 'app', 'router', 'store', 'pinia', 'createApp',
                      'defineComponent', 'ref', 'reactive', 'computed', 'watch',
                      // Ant Design 关键变量
                      'message', 'notification', 'Modal', 'Form', 'Table',
                      'Input', 'Button', 'Select', 'DatePicker', 'Upload',
                      // 微前端关键变量
                      '__POWERED_BY_QIANKUN__', '__INJECTED_PUBLIC_PATH_BY_QIANKUN__',
                      // 构建工具变量
                      '__APP_INFO__', '__VITE_PLUGIN_THEME__',
                      // 工具库关键变量
                      'axios', 'dayjs', 'lodash', '_', 'crypto',
                      // 关键原型方法
                      'constructor', 'prototype', 'toString', 'valueOf', 'hasOwnProperty',
                      // 重要的全局方法
                      'addEventListener', 'removeEventListener', 'setTimeout', 'clearTimeout',
                      'setInterval', 'clearInterval', 'Promise', 'fetch'
                    ],
                    // 不混淆属性名，避免框架属性访问问题
                  },
                },
              },
              minify: true,
              sourceMaps: false,
            })
            
            const originalSize = chunk.code.length
            const compressedSize = result.code.length
            const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1)
            
            chunk.code = result.code
            processedFiles++
            
            // 只对大文件显示压缩信息
            if (originalSize > 50000) {
              console.log(`  ✓ ${fileName}: ${(originalSize / 1024).toFixed(1)}KB → ${(compressedSize / 1024).toFixed(1)}KB (-${compressionRatio}%)`)
            }
          } catch (error) {
            console.warn(`⚠️  SWC 压缩失败 ${fileName}:`, error)
          }
        }
      }
      
      const endTime = Date.now()
      console.log(`✨ SWC 压缩完成: 处理了 ${processedFiles} 个文件，耗时 ${endTime - startTime}ms`)
    },
  }
}
