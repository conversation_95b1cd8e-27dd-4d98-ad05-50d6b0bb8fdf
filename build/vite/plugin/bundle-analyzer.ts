import type { Plugin, OutputBundle, OutputChunk, OutputAsset } from 'vite';
import { gzipSync } from 'zlib';

interface BundleInfo {
  name: string;
  size: number;
  gzipSize: number;
  type: 'chunk' | 'asset';
  imports?: string[];
  modules?: string[];
}

export function bundleAnalyzerPlugin(): Plugin {
  const bundles: BundleInfo[] = [];

  const formatSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`;
  };

  const analyzeBundle = (fileName: string, info: OutputChunk | OutputAsset): BundleInfo => {
    const source = 'code' in info ? info.code : info.source;
    const sourceString = typeof source === 'string' ? source : source.toString();
    const size = Buffer.byteLength(sourceString, 'utf8');
    const gzipSize = gzipSync(sourceString).length;

    const bundleInfo: BundleInfo = {
      name: fileName,
      size,
      gzipSize,
      type: info.type === 'chunk' ? 'chunk' : 'asset'
    };

    if (info.type === 'chunk') {
      bundleInfo.imports = info.imports;
      bundleInfo.modules = Object.keys(info.modules || {});
    }

    return bundleInfo;
  };

  return {
    name: 'bundle-analyzer',
    
    generateBundle(options, bundle: OutputBundle) {
      // 分析每个输出文件
      Object.entries(bundle).forEach(([fileName, info]) => {
        const bundleInfo = analyzeBundle(fileName, info);
        bundles.push(bundleInfo);
      });
    },

    writeBundle() {
      if (bundles.length === 0) return;

      console.log('\n📦 构建产物分析:');
      
      // 按大小排序
      const sortedBundles = [...bundles].sort((a, b) => b.size - a.size);
      
      // 显示主要文件
      const mainBundles = sortedBundles.filter(b => b.type === 'chunk').slice(0, 10);
      if (mainBundles.length > 0) {
        console.log('\n📊 主要 JavaScript 文件 (Top 10):');
        console.table(mainBundles.map(bundle => ({
          文件名: bundle.name,
          原始大小: formatSize(bundle.size),
          Gzip大小: formatSize(bundle.gzipSize),
          压缩率: `${((1 - bundle.gzipSize / bundle.size) * 100).toFixed(1)}%`,
          模块数: bundle.modules?.length || 0
        })));
      }

      // 显示资源文件
      const assets = sortedBundles.filter(b => b.type === 'asset').slice(0, 5);
      if (assets.length > 0) {
        console.log('\n🎨 主要资源文件 (Top 5):');
        console.table(assets.map(asset => ({
          文件名: asset.name,
          大小: formatSize(asset.size),
          Gzip大小: formatSize(asset.gzipSize)
        })));
      }

      // 总体统计
      const totalSize = bundles.reduce((sum, b) => sum + b.size, 0);
      const totalGzipSize = bundles.reduce((sum, b) => sum + b.gzipSize, 0);
      const chunkCount = bundles.filter(b => b.type === 'chunk').length;
      const assetCount = bundles.filter(b => b.type === 'asset').length;

      console.log('\n📈 构建产物统计:');
      console.table({
        '总体': {
          '文件数量': `${bundles.length} (${chunkCount} JS + ${assetCount} 资源)`,
          '原始大小': formatSize(totalSize),
          'Gzip大小': formatSize(totalGzipSize),
          '压缩率': `${((1 - totalGzipSize / totalSize) * 100).toFixed(1)}%`
        }
      });

      // 性能建议
      console.log('\n💡 构建产物建议:');
      
      const largeBundles = bundles.filter(b => b.size > 1024 * 1024); // > 1MB
      if (largeBundles.length > 0) {
        console.log('⚠️ 发现大型文件 (>1MB):');
        largeBundles.forEach(bundle => {
          console.log(`  - ${bundle.name}: ${formatSize(bundle.size)}`);
        });
        console.log('💡 建议: 考虑进一步拆分大型文件或使用动态导入');
      }

      const poorCompressionBundles = bundles.filter(b => 
        b.size > 100 * 1024 && (b.gzipSize / b.size) > 0.7
      );
      if (poorCompressionBundles.length > 0) {
        console.log('⚠️ 压缩效果不佳的文件 (压缩率<30%):');
        poorCompressionBundles.forEach(bundle => {
          const compressionRatio = ((1 - bundle.gzipSize / bundle.size) * 100).toFixed(1);
          console.log(`  - ${bundle.name}: ${compressionRatio}% 压缩率`);
        });
        console.log('💡 建议: 检查是否包含已压缩的内容或二进制数据');
      }

      if (chunkCount > 20) {
        console.log('⚠️ JavaScript 文件数量较多，可能影响 HTTP/1.1 性能');
        console.log('💡 建议: 在 HTTP/1.1 环境下考虑合并部分小文件');
      } else if (chunkCount < 5) {
        console.log('💡 建议: 可以考虑进一步拆分以提高缓存效率');
      } else {
        console.log('✅ 文件数量适中，有利于缓存和并行加载');
      }

      // 清空数组以备下次使用
      bundles.length = 0;
    }
  };
}
