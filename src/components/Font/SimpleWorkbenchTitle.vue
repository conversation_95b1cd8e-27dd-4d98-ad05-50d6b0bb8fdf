<template>
  <div class="simple-workbench-title" :style="titleStyle">
    <slot />
  </div>
</template>

<script setup lang="ts" name="SimpleWorkbenchTitle">
import { computed } from 'vue';

interface Props {
  fontSize?: string;
  color?: string;
  lineHeight?: string;
}

const props = withDefaults(defineProps<Props>(), {
  fontSize: '74px',
  color: '#000000',
  lineHeight: '84px',
});

// 标题样式
const titleStyle = computed(() => ({
  fontSize: props.fontSize,
  color: props.color,
  lineHeight: props.lineHeight,
}));
</script>

<style scoped>
/* 直接在 CSS 中定义字体 */
@font-face {
  font-family: 'AlimamaShuHeiTi-Bold';
  src: url('/src/assets/fonts/AlimamaShuHeiTi-Bold.woff2') format('woff2');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

.simple-workbench-title {
  font-family: 'AlimamaShuHeiTi-Bold', 'PingFang SC', Arial, sans-serif;
  font-weight: bold;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .simple-workbench-title {
    font-size: 48px !important;
    line-height: 56px !important;
  }
}

@media (max-width: 480px) {
  .simple-workbench-title {
    font-size: 36px !important;
    line-height: 42px !important;
  }
}
</style>
