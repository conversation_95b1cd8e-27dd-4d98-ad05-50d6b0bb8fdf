/**
 * Tooltip配置接口
 */
export interface TooltipConfig {
  /** 是否显示百分比 */
  showPercentage?: boolean;
  /** 是否显示总计 */
  showTotal?: boolean;
  /** 需要排除的系列类型 */
  excludeSeriesTypes?: string[];
  /** 特殊系列配置 */
  specialSeries?: { type: string; label: string };
  /** 自定义额外信息提供者 */
  extraInfoProvider?: (axisValue: string, params: any[]) => string;
  /** 自定义值格式化器 */
  valueFormatter?: (value: number, percent?: number) => string;
  /** 总计标签 */
  totalLabel?: string;
}

/**
 * 统一的Tooltip格式化器工厂函数
 * 通过配置对象支持所有tooltip场景，消除代码重复
 */
export const createTooltipFormatter = (config: TooltipConfig = {}): ((params: any) => string) => {
  const {
    showPercentage = false,
    showTotal = false,
    excludeSeriesTypes = [],
    specialSeries = null,
    extraInfoProvider = null,
    valueFormatter = null,
    totalLabel = '总计',
  } = config;

  return (params: any): string => {
    if (!Array.isArray(params) || params.length === 0) {
      return '';
    }

    const axisValue = params[0].axisValue;
    let tooltipContent = `<div style="margin-bottom: 4px; font-weight: bold;">${axisValue}</div>`;

    // 过滤数据
    const filteredParams = excludeSeriesTypes.length > 0 ? params.filter((param: any) => !excludeSeriesTypes.includes(param.seriesType)) : params;

    const specialParam = specialSeries ? params.find((param: any) => param.seriesType === specialSeries.type) : null;

    // 显示总计
    if (showTotal) {
      const total = specialParam ? specialParam.data.value : filteredParams.reduce((sum: number, param: any) => sum + (param.data.value || 0), 0);
      tooltipContent += `<div style="margin-bottom: 4px;">${totalLabel}: ${total}</div>`;
    }

    // 显示常规数据
    filteredParams.forEach((param: any) => {
      const value = param.data.value || 0;
      let displayText = param.seriesName + ': ';

      if (valueFormatter) {
        // 使用自定义格式化器
        const percent = param.data.percent || 0;
        displayText += valueFormatter(value, percent);
      } else if (showPercentage) {
        // 计算并显示百分比
        const total =
          showTotal && specialParam ? specialParam.data.value : filteredParams.reduce((sum: number, p: any) => sum + (p.data.value || 0), 0);
        const percent = param.data.percent || (total > 0 ? calculatePercentage(value, total) : 0);
        displayText += formatValueWithPercentage(value, percent);
      } else {
        // 仅显示数值
        displayText += value.toString();
      }

      tooltipContent += `
        <div style="display: flex; align-items: center; margin: 2px 0;">
          <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
          <span>${displayText}</span>
        </div>
      `;
    });

    // 显示特殊系列数据
    if (specialParam && specialSeries) {
      tooltipContent += `
        <div style="display: flex; align-items: center; margin: 2px 0;">
          <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${specialParam.color};"></span>
          <span>${specialParam.seriesName}: ${specialParam.data.value}</span>
        </div>
      `;
    }

    // 添加额外信息
    if (extraInfoProvider) {
      const extraInfo = extraInfoProvider(axisValue, params);
      if (extraInfo) {
        tooltipContent += `<div style="margin-top: 4px;">${extraInfo}</div>`;
      }
    }

    return tooltipContent;
  };
};

/**
 * 通用的百分比计算函数
 */
export const calculatePercentage = (value: number, total: number): string => {
  return total > 0 ? ((value / total) * 100).toFixed(1) : '0.0';
};

/**
 * 通用的标签和tooltip格式化函数
 */
export const formatValueWithPercentage = (value: number, percent: string | number): string => {
  return `${value} (${percent}%)`;
};

/**
 * 标准化尺寸值 - 支持数字、百分比、视口单位等
 * @param value 尺寸值，可以是数字或字符串
 * @returns 标准化的CSS尺寸字符串
 */
export const normalizeSizeValue = (value: number | string | undefined): string | undefined => {
  if (value === undefined || value === null) {
    return undefined;
  }

  // 如果是数字，添加px单位
  if (typeof value === 'number') {
    return `${value}px`;
  }

  // 如果是字符串，直接返回（支持百分比、vh、vw等单位）
  if (typeof value === 'string') {
    return value;
  }

  return undefined;
};

/**
 * API数据转换工具函数
 */

// 导入相关类型
import type { ChartDataItem } from '../types/statisticDashboard';
import type { AllClueSourceResponseType } from '../api/index';
import { ClueSourceEnum } from '../enums';

/**
 * 将API返回的一级线索来源数据转换为图表数据格式
 */
export function transformOneSourceDataToChartData(apiResponse: AllClueSourceResponseType): ChartDataItem[] {
  if (!apiResponse?.clueOneSourceResponseList?.length) {
    console.warn('API响应中没有一级线索来源数据');
    return [];
  }

  // 取最新日期的数据
  const latestData = apiResponse.clueOneSourceResponseList[0];
  if (!latestData?.clueOneSourceDtoList?.length) {
    console.warn('最新日期的一级线索来源数据为空');
    return [];
  }

  console.log('转换一级线索来源数据，原始数据：', latestData.clueOneSourceDtoList);

  const chartData = latestData.clueOneSourceDtoList.map(item => ({
    name: item.oneSourceName,
    value: item.oneSourceCount,
    percentage: item.oneSourcePercent,
    id: item.oneSourceId,
    // 添加颜色映射
    color: getColorBySourceId(item.oneSourceId),
    // 添加原始数据引用，便于调试
    _rawData: item,
  }));

  console.log('转换后的一级线索来源图表数据：', chartData);
  return chartData;
}

/**
 * 将API返回的二级线索来源数据转换为图表数据格式
 */
export function transformTwoSourceDataToChartData(apiResponse: AllClueSourceResponseType): ChartDataItem[] {
  if (!apiResponse?.clueTwoSourceResponseList?.length) {
    console.warn('API响应中没有二级线索来源数据');
    return [];
  }

  // 取最新日期的数据
  const latestData = apiResponse.clueTwoSourceResponseList[0];
  if (!latestData?.clueTwoSourceDtoList?.length) {
    console.warn('最新日期的二级线索来源数据为空');
    return [];
  }

  console.log('转换二级线索来源数据，原始数据：', latestData.clueTwoSourceDtoList);

  const chartData = latestData.clueTwoSourceDtoList.map(item => ({
    name: item.twoSourceName,
    value: item.twoSourceCount,
    percentage: item.twoSourcePercent,
    id: item.twoSourceId,
    parentName: item.oneSourceName,
    // 添加颜色映射
    color: getColorBySourceId(item.twoSourceId),
    // 添加原始数据引用，便于调试
    _rawData: item,
  }));

  console.log('转换后的二级线索来源图表数据：', chartData);
  return chartData;
}

/**
 * 根据来源ID获取对应的颜色
 */
export function getColorBySourceId(sourceId: string): string {
  // 颜色映射表，基于ClueSourceEnum枚举
  const colorMap: Record<string, string> = {
    // 一级来源颜色
    [ClueSourceEnum.ONE_SOURCE_ONLINE_PUB]: '#5470c6', // 线上公域 - 蓝色
    [ClueSourceEnum.ONE_SOURCE_ONLINE_PRI]: '#91cc75', // 线上私域 - 绿色
    [ClueSourceEnum.ONE_SOURCE_OFFLINE_PRI]: '#fac858', // 线下私域 - 黄色

    // 二级来源颜色
    [ClueSourceEnum.TWO_SOURCE_ONLINE_PRI_APP]: '#9c27b0', // APP - 紫色
    [ClueSourceEnum.TWO_SOURCE_ONLINE_PRI_WEB]: '#e91e63', // 官网 - 粉色
    [ClueSourceEnum.TWO_SOURCE_ONLINE_PRI_CUSTOM]: '#00bcd4', // 客服中心 - 青色
    [ClueSourceEnum.TWO_SOURCE_OFFLINE_PRI_MARKETING]: '#ff9800', // 营销活动 - 橙色
    [ClueSourceEnum.TWO_SOURCE_OFFLINE_PRI_CARSHOW]: '#795548', // 车展 - 棕色
    [ClueSourceEnum.TWO_SOURCE_ONLINE_PUB_SOCIAL]: '#607d8b', // 社媒 - 蓝灰色
    [ClueSourceEnum.TWO_SOURCE_ONLINE_PUB_VERTICAL]: '#4caf50', // 垂媒 - 绿色
  };

  const color = colorMap[sourceId];
  if (!color) {
    console.warn(`未找到来源ID ${sourceId} 对应的颜色，使用默认颜色`);
    return '#666666'; // 默认颜色
  }

  return color;
}

/**
 * 验证API响应数据的有效性
 */
export function validateAPIResponse(response: any, dataType: 'oneSource' | 'twoSource'): boolean {
  if (!response || !response.success) {
    console.error('API响应失败或无效：', response);
    return false;
  }

  if (!response.result) {
    console.error('API响应结果为空：', response);
    return false;
  }

  const listKey = dataType === 'oneSource' ? 'clueOneSourceResponseList' : 'clueTwoSourceResponseList';
  const dtoKey = dataType === 'oneSource' ? 'clueOneSourceDtoList' : 'clueTwoSourceDtoList';

  if (!response.result[listKey] || !Array.isArray(response.result[listKey])) {
    console.error(`API响应中缺少${listKey}字段或格式错误：`, response.result);
    return false;
  }

  if (response.result[listKey].length === 0) {
    console.warn(`API响应中${listKey}为空数组`);
    return false;
  }

  const latestData = response.result[listKey][0];
  if (!latestData[dtoKey] || !Array.isArray(latestData[dtoKey])) {
    console.error(`API响应中缺少${dtoKey}字段或格式错误：`, latestData);
    return false;
  }

  return true;
}

/**
 * 创建错误时的默认数据
 */
export function createFallbackChartData(dataType: 'allClues' | 'validClues'): ChartDataItem[] {
  const fallbackData: ChartDataItem[] = [
    {
      name: '暂无数据',
      value: 0,
      percentage: 0,
      id: 'no-data',
      color: '#cccccc',
    }
  ];

  console.log(`创建${dataType}的默认数据：`, fallbackData);
  return fallbackData;
}
