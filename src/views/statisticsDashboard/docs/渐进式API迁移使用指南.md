# 统计看板渐进式API迁移使用指南

## 📋 概述

本指南介绍如何使用新的渐进式API迁移系统，将统计看板的图表配置从Mock数据平滑迁移到真实API调用。

## 🏗️ 架构设计

### 核心组件

1. **useAPIChartConfig** - API图表配置管理Hook
2. **useTabConfigManager** - Tab配置管理器（已集成API配置）
3. **ChartMigrationStatus** - 迁移状态枚举

### 迁移模式

| 模式 | 说明 | 使用场景 |
|------|------|----------|
| `MOCK` | 纯Mock数据 | 开发环境、API不可用时 |
| `HYBRID` | API优先，Mock fallback | 推荐的生产环境模式 |
| `API` | 纯API数据 | API完全稳定后 |

## 🚀 快速开始

### 1. 基础集成

在主组件中集成API配置管理器：

```typescript
// 在统计看板主组件中
import { useTabConfigManager } from './hooks/useTabConfigManager';
import { useFilters } from './hooks/useFilters';

export default defineComponent({
  setup() {
    // 初始化配置管理器
    const tabConfigManager = useTabConfigManager();
    const filtersHook = useFilters();
    
    // 设置筛选器Hook到API配置管理器
    tabConfigManager.setFiltersHook(filtersHook);
    
    // 初始化时更新线索来源Tab配置
    onMounted(async () => {
      try {
        await tabConfigManager.updateSourceOfCluesTab();
        console.log('线索来源Tab配置已更新为API模式');
      } catch (error) {
        console.error('更新Tab配置失败:', error);
      }
    });
    
    return {
      tabConfigManager,
      filtersHook,
    };
  }
});
```

### 2. 渐进式迁移控制

使用迁移控制器管理迁移过程：

```typescript
// 获取迁移控制器
const { migrationController } = tabConfigManager.apiChartConfig;

// 第一步：启用混合模式（推荐）
migrationController.enableHybridMode();

// 第二步：确认API稳定后切换到纯API模式
migrationController.switchToAPIMode();

// 紧急情况：回退到Mock模式
migrationController.fallbackToMockMode();

// 查看迁移状态
const status = migrationController.getStatus();
console.log('迁移状态:', status);
```

### 3. 手动控制单个图表

```typescript
const { apiChartConfig } = tabConfigManager;

// 设置特定图表的迁移状态
apiChartConfig.setChartMigrationStatus('sourceOfClues', ChartMigrationStatus.HYBRID);

// 启用/禁用图表迁移
apiChartConfig.toggleChartMigration('sourceOfClues', true);

// 刷新API配置
await apiChartConfig.refreshAPIConfig('sourceOfClues');
```

## 📊 迁移流程

### 推荐的迁移步骤

```mermaid
graph TD
    A[开发环境] --> B[启用混合模式]
    B --> C[测试API调用]
    C --> D{API稳定?}
    D -->|是| E[切换到API模式]
    D -->|否| F[修复API问题]
    F --> C
    E --> G[生产环境部署]
    G --> H[监控运行状态]
    H --> I{出现问题?}
    I -->|是| J[回退到混合模式]
    I -->|否| K[迁移完成]
    J --> L[修复问题]
    L --> E
```

### 1. 开发阶段

```typescript
// 开发环境配置
const setupDevelopment = () => {
  const { migrationController } = tabConfigManager.apiChartConfig;
  
  // 启用混合模式进行测试
  migrationController.enableHybridMode();
  
  // 监听错误状态
  watch(() => tabConfigManager.apiChartConfig.errorStates, (errors) => {
    Object.entries(errors).forEach(([chartId, error]) => {
      if (error) {
        console.warn(`图表 ${chartId} API调用失败:`, error);
      }
    });
  });
};
```

### 2. 测试阶段

```typescript
// 测试API调用
const testAPIIntegration = async () => {
  const { apiChartConfig } = tabConfigManager;
  
  try {
    // 测试全量线索来源
    const allCluesConfig = await apiChartConfig.generateSourceOfCluesConfigFromAPI('sourceOfAllClues');
    console.log('全量线索API测试成功:', allCluesConfig);
    
    // 测试有效线索来源
    const validCluesConfig = await apiChartConfig.generateSourceOfCluesConfigFromAPI('sourceOfEffectiveClues');
    console.log('有效线索API测试成功:', validCluesConfig);
    
    return true;
  } catch (error) {
    console.error('API测试失败:', error);
    return false;
  }
};
```

### 3. 生产部署

```typescript
// 生产环境配置
const setupProduction = () => {
  const { migrationController } = tabConfigManager.apiChartConfig;
  
  // 生产环境使用混合模式确保稳定性
  migrationController.enableHybridMode();
  
  // 设置错误监控
  const errorHandler = (chartId: string, error: string) => {
    // 发送错误报告到监控系统
    console.error(`生产环境图表错误 [${chartId}]:`, error);
    
    // 可以集成到错误监控服务
    // errorReportingService.report({ chartId, error, timestamp: new Date() });
  };
  
  // 监听错误状态变化
  watch(() => tabConfigManager.apiChartConfig.errorStates, (errors) => {
    Object.entries(errors).forEach(([chartId, error]) => {
      if (error) {
        errorHandler(chartId, error);
      }
    });
  });
};
```

## 🔧 配置选项

### 迁移配置

```typescript
interface ChartMigrationConfig {
  chartId: string;           // 图表ID
  status: ChartMigrationStatus; // 迁移状态
  enabled: boolean;          // 是否启用
  lastUpdated: Date;         // 最后更新时间
  error?: string;            // 错误信息
}
```

### 状态监控

```typescript
// 获取迁移统计
const stats = apiChartConfig.getMigrationStats();
console.log('迁移统计:', {
  total: stats.total,        // 总图表数
  api: stats.api,           // API模式图表数
  hybrid: stats.hybrid,     // 混合模式图表数
  mock: stats.mock,         // Mock模式图表数
  errors: stats.errors      // 错误数量
});

// 获取加载状态
const loadingStates = apiChartConfig.loadingStates;
console.log('加载状态:', loadingStates);

// 获取错误状态
const errorStates = apiChartConfig.errorStates;
console.log('错误状态:', errorStates);
```

## 🛡️ 错误处理

### 自动Fallback机制

系统提供了完善的错误处理和fallback机制：

1. **API调用失败** → 自动使用Mock数据
2. **数据格式错误** → 显示错误提示，使用默认配置
3. **网络超时** → 重试机制 + Mock fallback

### 手动错误恢复

```typescript
// 清除错误状态
const clearErrors = () => {
  Object.keys(apiChartConfig.errorStates).forEach(chartId => {
    apiChartConfig.errorStates[chartId] = null;
  });
};

// 重新尝试API调用
const retryAPICall = async (chartId: string) => {
  try {
    clearErrors();
    await apiChartConfig.refreshAPIConfig(chartId);
    message.success('API调用恢复成功');
  } catch (error) {
    message.error('API调用仍然失败，请检查网络连接');
  }
};
```

## 📈 性能优化

### 缓存机制

```typescript
// API配置会自动缓存，避免重复调用
const cachedConfig = apiChartConfig.apiConfigCache['sourceOfClues_sourceOfAllClues'];

// 手动清除缓存
const clearCache = () => {
  Object.keys(apiChartConfig.apiConfigCache).forEach(key => {
    delete apiChartConfig.apiConfigCache[key];
  });
};
```

### 懒加载

```typescript
// 只有在需要时才加载API配置
const lazyLoadConfig = async (chartId: string) => {
  if (!apiChartConfig.apiConfigCache[chartId]) {
    await apiChartConfig.getChartConfig(chartId);
  }
  return apiChartConfig.apiConfigCache[chartId];
};
```

## 🔍 调试指南

### 开启调试日志

所有API调用和配置生成都有详细的console日志输出：

```typescript
// 查看API调用日志
// 控制台会显示：
// "正在从API生成 sourceOfAllClues 图表配置..."
// "API参数: { startDate: '2025-08-01', ... }"
// "API数据获取成功，数据量: 3"

// 查看迁移状态日志
// "图表 sourceOfClues 迁移状态已设置为: hybrid"
// "线索来源Tab配置已更新，图表数量: 6"
```

### 常见问题排查

1. **API调用失败**
   - 检查网络连接
   - 验证API接口地址
   - 确认筛选参数格式

2. **配置生成失败**
   - 检查API返回数据格式
   - 验证数据转换逻辑
   - 确认图表类型定义

3. **迁移状态异常**
   - 检查迁移配置
   - 验证Hook集成
   - 确认组件初始化顺序

## 📝 最佳实践

1. **渐进式迁移** - 先使用混合模式，确认稳定后再切换到API模式
2. **错误监控** - 设置完善的错误监控和报警机制
3. **性能监控** - 监控API响应时间和成功率
4. **回退策略** - 准备快速回退到Mock模式的方案
5. **测试覆盖** - 确保API和Mock模式都有充分的测试

通过这个渐进式迁移系统，你可以安全、平滑地将统计看板从Mock数据迁移到真实API，同时保持系统的稳定性和用户体验。
