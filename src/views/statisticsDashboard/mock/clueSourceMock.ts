// 线索来源 mock数据

import { ChartConfig, ChartDataItem, DrillDownConfig, DrillDownDataProvider } from '../types/statisticDashboard';
import { calculatePercentage, createTooltipFormatter, formatValueWithPercentage } from '../utils';

import { useI18n } from '../../../hooks/web/useI18n';

const { t } = useI18n('common');

/**
 * 渠道配置定义 - 通用的渠道层级配置
 */
export interface ChannelConfig {
  key: string;
  name: string;
  color: string;
  children?: ChannelConfig[];
}

const clueSourceChannelChildConfig: ChannelConfig[] = [
  { key: 'app', name: 'APP', color: '#9c27b0' }, // 紫色
  { key: 'website', name: t('officeWeb'), color: '#e91e63' }, // 粉色
  { key: 'serviceCenter', name: t('customerServiceCenter'), color: '#00bcd4' }, // 青色
];

/**
 * 一级线索来源渠道配置 - 支持任意层级扩展
 */
export const clueSourceChannelConfig: ChannelConfig[] = [
  {
    key: 'onlinePublic',
    name: t('onlinePublicDomain'),
    color: '#5470c6', // 蓝色
    children: clueSourceChannelChildConfig,
  },
  {
    key: 'onlinePrivate',
    name: t('onlinePrivateDomain'),
    color: '#91cc75', // 绿色
    children: clueSourceChannelChildConfig,
  },
  {
    key: 'offlinePrivate',
    name: t('offlinePrivateDomain'),
    color: '#fac858', // 黄色
    children: clueSourceChannelChildConfig,
  },
];

/**
 * UTM渠道配置定义 - utm_source维度
 */
export const utmSourceChannelConfig: ChannelConfig[] = [
  {
    key: 'uol',
    name: 'UOL',
    color: '#5470c6', // 蓝色
  },
  {
    key: 'google',
    name: 'Google',
    color: '#91cc75', // 绿色
  },
  {
    key: 'weach',
    name: 'Weach',
    color: '#fac858', // 黄色
  },
  {
    key: 'metaAds',
    name: 'Meta Ads',
    color: '#ee6666', // 红色
  },
];

/**
 * UTM渠道配置定义 - utm_medium维度
 */
export const utmMediumChannelConfig: ChannelConfig[] = [
  {
    key: 'cpc',
    name: 'CPC',
    color: '#5470c6', // 蓝色
  },
  {
    key: 'social',
    name: 'Social',
    color: '#91cc75', // 绿色
  },
  {
    key: 'email',
    name: 'Email',
    color: '#fac858', // 黄色
  },
  {
    key: 'organic',
    name: 'Organic',
    color: '#ee6666', // 红色
  },
  {
    key: 'referral',
    name: 'Referral',
    color: '#73c0de', // 粉色系
  },
];

/**
 * 通用的月度数据接口
 */
export interface MonthlyChannelData {
  name: string; // 月份名称
  value: number; // 总值
  type: 'period';
  channels: Record<string, number>; // 各渠道数据
  subChannels?: Record<string, Record<string, number>>; // 子渠道数据
}

/**
 * 全量线索来源数据（支持通用下探）
 */
export const sourceOfAllCluesData: MonthlyChannelData[] = [
  {
    name: '2023-1',
    value: 30,
    type: 'period',
    channels: {
      onlinePublic: 10,
      onlinePrivate: 8,
      offlinePrivate: 12,
    },
    subChannels: {
      onlinePublic: { app: 4, website: 3, serviceCenter: 3 },
      onlinePrivate: { app: 3, website: 2, serviceCenter: 3 },
      offlinePrivate: { app: 5, website: 4, serviceCenter: 3 },
    },
  },
  {
    name: '2023-2',
    value: 37,
    type: 'period',
    channels: {
      onlinePublic: 15,
      onlinePrivate: 12,
      offlinePrivate: 10,
    },
    subChannels: {
      onlinePublic: { app: 6, website: 5, serviceCenter: 4 },
      onlinePrivate: { app: 5, website: 3, serviceCenter: 4 },
      offlinePrivate: { app: 4, website: 3, serviceCenter: 3 },
    },
  },
  {
    name: '2023-3',
    value: 36,
    type: 'period',
    channels: {
      onlinePublic: 12,
      onlinePrivate: 10,
      offlinePrivate: 14,
    },
    subChannels: {
      onlinePublic: { app: 5, website: 4, serviceCenter: 3 },
      onlinePrivate: { app: 4, website: 3, serviceCenter: 3 },
      offlinePrivate: { app: 6, website: 4, serviceCenter: 4 },
    },
  },
  {
    name: '2023-4',
    value: 38,
    type: 'period',
    channels: {
      onlinePublic: 8,
      onlinePrivate: 14,
      offlinePrivate: 16,
    },
    subChannels: {
      onlinePublic: { app: 3, website: 2, serviceCenter: 3 },
      onlinePrivate: { app: 6, website: 4, serviceCenter: 4 },
      offlinePrivate: { app: 7, website: 5, serviceCenter: 4 },
    },
  },
  {
    name: '2023-5',
    value: 39,
    type: 'period',
    channels: {
      onlinePublic: 18,
      onlinePrivate: 9,
      offlinePrivate: 12,
    },
    subChannels: {
      onlinePublic: { app: 8, website: 5, serviceCenter: 5 },
      onlinePrivate: { app: 4, website: 2, serviceCenter: 3 },
      offlinePrivate: { app: 5, website: 4, serviceCenter: 3 },
    },
  },
  {
    name: '2023-6',
    value: 40,
    type: 'period',
    channels: {
      onlinePublic: 14,
      onlinePrivate: 16,
      offlinePrivate: 10,
    },
    subChannels: {
      onlinePublic: { app: 6, website: 4, serviceCenter: 4 },
      onlinePrivate: { app: 7, website: 5, serviceCenter: 4 },
      offlinePrivate: { app: 4, website: 3, serviceCenter: 3 },
    },
  },
  {
    name: '2023-7',
    value: 42,
    type: 'period',
    channels: {
      onlinePublic: 16,
      onlinePrivate: 12,
      offlinePrivate: 14,
    },
    subChannels: {
      onlinePublic: { app: 7, website: 5, serviceCenter: 4 },
      onlinePrivate: { app: 5, website: 3, serviceCenter: 4 },
      offlinePrivate: { app: 6, website: 4, serviceCenter: 4 },
    },
  },
];

/**
 * 有效线索来源数据（数据可能与全量不同，支持通用下探）
 */
export const sourceOfEffectiveCluesData: MonthlyChannelData[] = [
  {
    name: '2023-1',
    value: 28,
    type: 'period',
    channels: {
      onlinePublic: 9,
      onlinePrivate: 7,
      offlinePrivate: 12,
    },
    subChannels: {
      onlinePublic: { app: 3, website: 3, serviceCenter: 3 },
      onlinePrivate: { app: 3, website: 2, serviceCenter: 2 },
      offlinePrivate: { app: 5, website: 4, serviceCenter: 3 },
    },
  },
  {
    name: '2023-2',
    value: 35,
    type: 'period',
    channels: {
      onlinePublic: 14,
      onlinePrivate: 11,
      offlinePrivate: 10,
    },
    subChannels: {
      onlinePublic: { app: 6, website: 4, serviceCenter: 4 },
      onlinePrivate: { app: 5, website: 3, serviceCenter: 3 },
      offlinePrivate: { app: 4, website: 3, serviceCenter: 3 },
    },
  },
  {
    name: '2023-3',
    value: 34,
    type: 'period',
    channels: {
      onlinePublic: 11,
      onlinePrivate: 9,
      offlinePrivate: 14,
    },
    subChannels: {
      onlinePublic: { app: 4, website: 4, serviceCenter: 3 },
      onlinePrivate: { app: 4, website: 2, serviceCenter: 3 },
      offlinePrivate: { app: 6, website: 4, serviceCenter: 4 },
    },
  },
  {
    name: '2023-4',
    value: 36,
    type: 'period',
    channels: {
      onlinePublic: 7,
      onlinePrivate: 13,
      offlinePrivate: 16,
    },
    subChannels: {
      onlinePublic: { app: 2, website: 2, serviceCenter: 3 },
      onlinePrivate: { app: 6, website: 3, serviceCenter: 4 },
      offlinePrivate: { app: 7, website: 5, serviceCenter: 4 },
    },
  },
  {
    name: '2023-5',
    value: 37,
    type: 'period',
    channels: {
      onlinePublic: 17,
      onlinePrivate: 8,
      offlinePrivate: 12,
    },
    subChannels: {
      onlinePublic: { app: 8, website: 5, serviceCenter: 4 },
      onlinePrivate: { app: 3, website: 2, serviceCenter: 3 },
      offlinePrivate: { app: 5, website: 4, serviceCenter: 3 },
    },
  },
  {
    name: '2023-6',
    value: 38,
    type: 'period',
    channels: {
      onlinePublic: 13,
      onlinePrivate: 15,
      offlinePrivate: 10,
    },
    subChannels: {
      onlinePublic: { app: 5, website: 4, serviceCenter: 4 },
      onlinePrivate: { app: 6, website: 5, serviceCenter: 4 },
      offlinePrivate: { app: 4, website: 3, serviceCenter: 3 },
    },
  },
  {
    name: '2023-7',
    value: 40,
    type: 'period',
    channels: {
      onlinePublic: 15,
      onlinePrivate: 11,
      offlinePrivate: 14,
    },
    subChannels: {
      onlinePublic: { app: 6, website: 5, serviceCenter: 4 },
      onlinePrivate: { app: 4, website: 3, serviceCenter: 4 },
      offlinePrivate: { app: 6, website: 4, serviceCenter: 4 },
    },
  },
];

/**
 * UTM Source数据（utm_source维度）
 */
export const utmSourceData: MonthlyChannelData[] = [
  {
    name: '2025-07-24',
    value: 125,
    type: 'period',
    channels: {
      uol: 45,
      google: 32,
      weach: 18,
      metaAds: 15,
    },
  },
  {
    name: '2025-07-25',
    value: 135,
    type: 'period',
    channels: {
      uol: 48,
      google: 35,
      weach: 20,
      metaAds: 17,
    },
  },
  {
    name: '2025-07-26',
    value: 145,
    type: 'period',
    channels: {
      uol: 50,
      google: 38,
      weach: 22,
      metaAds: 20,
    },
  },
  {
    name: '2025-07-27',
    value: 146,
    type: 'period',
    channels: {
      uol: 48,
      google: 42,
      weach: 25,
      metaAds: 16,
    },
  },
  {
    name: '2025-07-28',
    value: 155,
    type: 'period',
    channels: {
      uol: 55,
      google: 40,
      weach: 28,
      metaAds: 17,
    },
  },
  {
    name: '2025-07-29',
    value: 175,
    type: 'period',
    channels: {
      uol: 58,
      google: 45,
      weach: 30,
      metaAds: 22,
    },
  },
  {
    name: '2025-07-30',
    value: 187,
    type: 'period',
    channels: {
      uol: 62,
      google: 48,
      weach: 32,
      metaAds: 25,
    },
  },
];

/**
 * UTM Medium数据（utm_medium维度）
 */
export const utmMediumData: MonthlyChannelData[] = [
  {
    name: '2025-07-24',
    value: 125,
    type: 'period',
    channels: {
      cpc: 50,
      social: 30,
      email: 20,
      organic: 15,
      referral: 10,
    },
  },
  {
    name: '2025-07-25',
    value: 135,
    type: 'period',
    channels: {
      cpc: 55,
      social: 32,
      email: 22,
      organic: 16,
      referral: 10,
    },
  },
  {
    name: '2025-07-26',
    value: 145,
    type: 'period',
    channels: {
      cpc: 58,
      social: 35,
      email: 24,
      organic: 18,
      referral: 10,
    },
  },
  {
    name: '2025-07-27',
    value: 146,
    type: 'period',
    channels: {
      cpc: 56,
      social: 38,
      email: 26,
      organic: 16,
      referral: 10,
    },
  },
  {
    name: '2025-07-28',
    value: 155,
    type: 'period',
    channels: {
      cpc: 62,
      social: 40,
      email: 28,
      organic: 15,
      referral: 10,
    },
  },
  {
    name: '2025-07-29',
    value: 175,
    type: 'period',
    channels: {
      cpc: 70,
      social: 45,
      email: 30,
      organic: 20,
      referral: 10,
    },
  },
  {
    name: '2025-07-30',
    value: 187,
    type: 'period',
    channels: {
      cpc: 75,
      social: 48,
      email: 32,
      organic: 22,
      referral: 10,
    },
  },
];

/**
 * 生成图表数据项（包含百分比）
 */
const generateChartDataItem = (name: string, value: number, percent: string, additionalProps: Record<string, any> = {}): any => {
  return {
    name,
    value: Number(value) || 0,
    percent: parseFloat(percent),
    ...additionalProps,
  };
};

// ==================== 具体图表的Tooltip配置 ====================

/**
 * 通用tooltip格式化函数（兼容现有用法）
 */
const formatTooltip = createTooltipFormatter({
  showPercentage: true,
});

/**
 * UTM图表tooltip格式化函数
 */
const formatUtmTooltip = createTooltipFormatter({
  showTotal: true,
  excludeSeriesTypes: ['line'],
  specialSeries: { type: 'line', label: t('totalTrend') },
  totalLabel: t('total'),
});

/**
 * 通用的渠道配置查找函数
 */
const findChannelConfig = (channelKey: string): ChannelConfig | null => {
  for (const config of clueSourceChannelConfig) {
    if (config.key === channelKey) {
      return config;
    }
    if (config.children) {
      for (const child of config.children) {
        if (child.key === channelKey) {
          return child;
        }
      }
    }
  }
  return null;
};

/**
 * 通用的下探数据生成器
 */
const generateDrillDownData = (parentChannelKey: string, originalDataSource: string, level: number): ChartDataItem[] => {
  // 获取原始的月份数据，支持更灵活的数据源检测
  const isAllClues = originalDataSource.includes('sourceOfAllClues') || originalDataSource === 'sourceOfAllClues';
  const monthlyData = isAllClues ? sourceOfAllCluesData : sourceOfEffectiveCluesData;

  if (level === 1) {
    // 一级下探：显示某个大类渠道的子渠道在各月份的分布
    const parentConfig = findChannelConfig(parentChannelKey);
    if (!parentConfig?.children) {
      console.warn(`未找到渠道配置: ${parentChannelKey}`);
      return [];
    }

    console.log(`生成下探数据 - 父渠道: ${parentChannelKey}, 子渠道数量: ${parentConfig.children.length}`);

    // 为每个子渠道生成月度数据
    const result: ChartDataItem[] = [];

    monthlyData.forEach((monthItem) => {
      parentConfig.children!.forEach((childConfig) => {
        const value = monthItem.subChannels?.[parentChannelKey]?.[childConfig.key] || 0;
        result.push({
          name: monthItem.name,
          value,
          type: 'period',
          channelKey: childConfig.key,
          channelName: childConfig.name,
          parentChannelKey,
          month: monthItem.name,
        });
      });
    });

    console.log(`下探数据生成完成，数据项数量: ${result.length}`);
    return result;
  }

  return [];
};

/**
 * 线索来源数据提供者（下探数据）
 */
export const clueSourceDataProvider: DrillDownDataProvider = {
  /**
   * 获取下探数据
   */
  async fetchData(parentData: ChartDataItem, level: number, config: ChartConfig): Promise<ChartDataItem[]> {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 300 + Math.random() * 200));

    // 获取点击的渠道键
    const parentChannelKey = parentData.channelKey || parentData.name;

    // 使用通用下探数据生成器
    const drillDownData = generateDrillDownData(parentChannelKey, config.dataSource, level);

    return drillDownData;
  },

  /**
   * 更新图表配置 - 根据下探层级动态配置图表
   */
  updateChartConfig(data: ChartDataItem[], level: number, _config: ChartConfig, parentData: ChartDataItem): Partial<ChartConfig> {
    if (level === 1) {
      // 一级下探：显示某个大类渠道的子渠道在各月份的分布
      const parentChannelKey = parentData.channelKey || parentData.name;
      const parentConfig = findChannelConfig(parentChannelKey);

      if (!parentConfig?.children) {
        console.warn(`下探配置更新失败 - 未找到渠道配置: ${parentChannelKey}`);
        return {};
      }

      console.log(`更新下探图表配置 - 父渠道: ${parentConfig.name}, 子渠道: ${parentConfig.children.map((c) => c.name).join(', ')}`);

      // 按子渠道分组数据
      const groupedData: Record<string, ChartDataItem[]> = {};
      parentConfig.children.forEach((childConfig) => {
        groupedData[childConfig.key] = data.filter((item) => item.channelKey === childConfig.key);
      });

      // 生成系列配置 - 每个子渠道一个系列
      const series = parentConfig.children.map((childConfig) => ({
        name: childConfig.name,
        type: 'bar',
        data:
          groupedData[childConfig.key]?.map((item) => ({
            name: item.month,
            value: item.value,
            itemStyle: {
              color: childConfig.color,
            },
          })) || [],
        itemStyle: {
          color: childConfig.color,
        },
        label: {
          show: true,
          position: 'top',
          formatter: (params: any) => `${params.data.value}`,
        },
      }));

      // 获取所有月份并排序
      const months = Array.from(new Set(data.map((item) => item.month))).sort();

      return {
        title: t('channelLeadAnalysis'), // 固定标题，符合图二的要求
        options: {
          title: {
            show: false,
          },
          color: parentConfig.children.map((child) => child.color),
          grid: {
            left: '4%',
            right: '4%',
            bottom: '15%',
            containLabel: true,
          },
          legend: {
            data: parentConfig.children.map((child) => child.name),
            bottom: '5%',
            left: 'center',
          },
          xAxis: {
            type: 'category',
            data: months,
          },
          yAxis: {
            type: 'value',
            name: t('numberOfClues'),
          },
          series: series as any,
          tooltip: {
            show: true, // 🔥 启用tooltip显示百分比，避免标签遮挡
            trigger: 'item',
            axisPointer: {
              type: 'shadow',
            },
            formatter: (params: any) => {
              return formatTooltip(params);
            },
          },
        },
      };
    }

    return {};
  },
};

/**
 * 线索来源下探配置
 */
export const clueSourceDrillConfig: DrillDownConfig = {
  enabled: true,
  currentLevel: 0,
  maxLevel: 1,
  dataStrategy: 'async', // 使用异步数据获取
  dataProvider: clueSourceDataProvider, // 使用通用数据提供者
  levels: [
    {
      level: 0,
      dataKey: 'channelKey',
      titleField: 'name',
      valueField: 'value',
      colorField: 'channelKey',
    },
    {
      level: 1,
      dataKey: 'subChannelKey',
      titleField: 'name',
      valueField: 'value',
      colorField: 'subChannelKey',
      parentKey: 'channelKey',
    },
  ],
};

/**
 * 生成线索来源图表配置（支持API数据和通用下探）
 */
export const generateSourceOfCluesChartConfig = (
  dataSource: 'sourceOfAllClues' | 'sourceOfEffectiveClues' = 'sourceOfAllClues',
  drillDownLevel: number = 0,
  drillDownData?: ChartDataItem[],
  parentData?: ChartDataItem,
  apiData?: ChartDataItem[] // 新增：支持传入API数据
): ChartConfig => {
  // 支持下探数据源的检测
  const actualDataSource = dataSource.includes('_drill_')
    ? dataSource.includes('sourceOfAllClues')
      ? 'sourceOfAllClues'
      : 'sourceOfEffectiveClues'
    : dataSource;

  const isAllClues = actualDataSource === 'sourceOfAllClues';

  // 优先使用API数据，如果没有则使用mock数据
  let data: ChartDataItem[];
  if (apiData && apiData.length > 0) {
    data = apiData;
    console.log('使用API数据生成图表配置：', data);
  } else {
    data = isAllClues ? sourceOfAllCluesData : sourceOfEffectiveCluesData;
    console.log('使用Mock数据生成图表配置：', data);
  }

  // 根据下探层级决定标题
  let title: string;
  if (drillDownLevel === 1) {
    title = t('channelLeadAnalysis');
  } else {
    title = isAllClues ? t('sourceOfAllClues') : t('effectiveSourcesOfClues');
  }

  // 根据下探层级决定图表配置
  const isSecondLevel = drillDownLevel === 1;

  // 检查数据格式：API数据 vs Mock数据
  const isAPIData = apiData && apiData.length > 0 && !data[0]?.channels;

  // 一级数据配置
  const firstLevelConfig = isAPIData ?
    generateAPIBasedFirstLevelConfig(data) :
    generateMockBasedFirstLevelConfig(data);

  /**
   * 生成基于API数据的一级配置
   */
  function generateAPIBasedFirstLevelConfig(apiData: ChartDataItem[]) {
    return {
      color: apiData.map(item => item.color || '#5470c6'),
      legend: {
        data: apiData.map(item => item.name),
        bottom: '5%',
        left: 'center',
      },
      series: [{
        name: title,
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '50%'],
        data: apiData.map(item => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.color || '#5470c6'
          },
          // 保留原始数据用于下探
          _rawData: item
        })),
        label: {
          show: true,
          formatter: (params: any) => {
            const percent = params.data._rawData?.percentage || 0;
            return `${params.name}\n${params.value} (${percent}%)`;
          },
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    };
  }

  /**
   * 生成基于Mock数据的一级配置
   */
  function generateMockBasedFirstLevelConfig(mockData: ChartDataItem[]) {
    return {
      color: clueSourceChannelConfig.map((config) => config.color),
      legend: {
        data: clueSourceChannelConfig.map((config) => config.name),
        bottom: '5%',
        left: 'center',
      },
      series: clueSourceChannelConfig.map((channelConfig) => ({
        name: channelConfig.name,
        type: 'bar',
        data: mockData.map((item) => {
          const value = item.channels?.[channelConfig.key] || 0;
          const percent = Array.isArray(item.value) ? '0.0' :
            (item.value > 0 ? calculatePercentage(value, item.value as number) : '0.0');
          return generateChartDataItem(item.name, value, percent, {
            channelKey: channelConfig.key, // 添加渠道键用于下探
          });
        }),
        itemStyle: {
          color: channelConfig.color,
        },
        label: {
          show: true,
          position: 'top',
          formatter: (params: any) => {
            return formatValueWithPercentage(params.data.value, params.data.percent);
          },
        },
      })),
    };
  }

  // 二级数据配置（下探后的配置）
  const secondLevelConfig = (() => {
    // 如果有下探数据，使用下探数据；否则使用空数据
    const actualDrillDownData = drillDownData || [];

    if (!parentData) {
      return { color: [], legend: { data: [] }, xAxisData: [], series: [] };
    }

    // 获取父级渠道配置
    const parentChannelKey = parentData.channelKey;
    const parentConfig = findChannelConfig(parentChannelKey);

    if (!parentConfig?.children) {
      return { color: [], legend: { data: [] }, xAxisData: [], series: [] };
    }

    // 按子渠道分组数据
    const groupedData: Record<string, ChartDataItem[]> = {};
    parentConfig.children.forEach((childConfig) => {
      groupedData[childConfig.key] = actualDrillDownData.filter((item) => item.channelKey === childConfig.key);
    });

    // 获取所有月份
    const months = Array.from(new Set(actualDrillDownData.map((item) => item.month))).sort();

    return {
      color: parentConfig.children.map((child) => child.color),
      legend: {
        data: parentConfig.children.map((child) => child.name),
        bottom: '5%',
      },
      xAxisData: months,
      series: parentConfig.children.map((childConfig) => ({
        name: childConfig.name,
        type: 'bar',
        data:
          groupedData[childConfig.key]?.map((item) => {
            // 🔥 修复：使用与图一一致的百分比计算逻辑
            // 计算该月份下所有子渠道的总和
            const monthlyTotal = actualDrillDownData
              .filter((dataItem) => dataItem.month === item.month)
              .reduce((sum, dataItem) => sum + (Number(dataItem.value) || 0), 0);

            const itemValue = Number(item.value) || 0;
            const percent = monthlyTotal > 0 ? calculatePercentage(itemValue, monthlyTotal) : '0.0';

            return generateChartDataItem(item.month, itemValue, percent, {
              channelKey: childConfig.key, // 添加渠道键用于下探
            });
          }) || [],
        itemStyle: {
          color: childConfig.color,
        },
        label: {
          show: true,
          position: 'top',
          formatter: (params: any) => {
            // 🔥 修复：显示数值和百分比
            return formatValueWithPercentage(params.data.value, params.data.percent);
          },
        },
      })),
    };
  })();

  const currentConfig = isSecondLevel ? secondLevelConfig : firstLevelConfig;

  return {
    id: 'sourceOfClues',
    type: 'bar',
    title,
    dataSource,
    // 添加下探配置
    drillDown: clueSourceDrillConfig,
    // 添加自定义属性来标识这是一个可切换的图表
    customProps: {
      switchable: true,
      currentDataSource: dataSource,
      alternativeDataSource: isAllClues ? 'sourceOfEffectiveClues' : 'sourceOfAllClues',
      alternativeTitle: isAllClues ? t('effectiveSourcesOfClues') : t('sourceOfAllClues'),
      drillDownLevel,
    },
    options: {
      // 禁用ECharts内置标题，使用自定义标题
      title: {
        show: false,
      },
      color: currentConfig.color,
      grid: {
        left: '4%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
      },
      legend: currentConfig.legend,
      xAxis: {
        type: 'category',
        data: isSecondLevel ? secondLevelConfig.xAxisData : data.map((item) => item.name),
        axisLabel: {
          interval: 0, // 强制显示所有标签
          rotate: 30, // 倾斜30度
        },
      },
      yAxis: {
        type: 'value',
        name: t('numberOfClues'),
      },
      series: currentConfig.series as any,
      tooltip: {
        show: true, // 🔥 启用tooltip显示百分比，避免标签遮挡
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: (params: any) => {
          return formatTooltip(params);
        },
      },
    },
    size: { height: 450 },
    position: { x: 0, y: 0 },
  };
};

/** 线索来源图表配置（默认使用全量线索数据） */
export const sourceOfCluesChartConfigs: ChartConfig[] = [generateSourceOfCluesChartConfig('sourceOfAllClues')];

/**
 * 生成UTM图表配置（支持utm_source和utm_medium维度切换）
 */
export const generateUtmChartConfig = (dataSource: 'utmSource' | 'utmMedium' = 'utmSource'): ChartConfig => {
  const isUtmSource = dataSource === 'utmSource';
  const data = isUtmSource ? utmSourceData : utmMediumData;
  const channelConfig = isUtmSource ? utmSourceChannelConfig : utmMediumChannelConfig;
  const title = isUtmSource ? 'utm_source' : 'utm_medium';

  // 生成堆叠系列配置（包含所有渠道）
  const stackSeries = channelConfig.map((channelItem) => ({
    name: channelItem.name,
    type: 'bar',
    stack: 'utm', // 堆叠配置
    data: data.map((item) => {
      const value = item.channels[channelItem.key] || 0;
      const percent = item.value > 0 ? calculatePercentage(value, item.value) : '0.0';
      return generateChartDataItem(item.name, value, percent, {
        channelKey: channelItem.key,
      });
    }),
    itemStyle: {
      color: channelItem.color,
    },
    label: {
      show: false, // 在堆叠图中通常不显示标签，避免重叠
    },
  }));

  // 生成总量趋势线系列配置（连接堆叠柱状图顶部）
  const trendSeries = {
    name: t('totalTrend'),
    type: 'line',
    data: data.map((item, index) => {
      // 计算所有渠道的总和
      const totalValue = channelConfig.reduce((sum, config) => sum + (item.channels[config.key] || 0), 0);
      return {
        name: item.name,
        value: totalValue, // 使用计算出的总和
        // 只在头尾显示标签
        label: {
          show: index === 0 || index === data.length - 1,
          position: 'top',
          formatter: '{c}',
          fontSize: 12,
          color: '#8B0000', // 深红色标签
        },
      };
    }),
    itemStyle: {
      color: '#8B0000', // 深红色数据点
    },
    lineStyle: {
      color: '#8B0000', // 深红色线条
      width: 2,
    },
    symbol: 'circle', // 使用圆形符号 是否有空心的圆型符号
    symbolSize: 6,
    yAxisIndex: 0, // 使用同一个Y轴
  };

  // 合并所有系列
  const series = [...stackSeries, trendSeries];

  return {
    id: 'utmChart',
    type: 'bar',
    title,
    dataSource,
    // 添加自定义属性来标识这是一个可切换的图表
    customProps: {
      switchable: true,
      currentDataSource: dataSource,
      alternativeDataSource: isUtmSource ? 'utmMedium' : 'utmSource',
      alternativeTitle: isUtmSource ? 'utm_medium' : 'utm_source',
    },
    options: {
      // 禁用ECharts内置标题，使用自定义标题
      title: {
        show: false,
      },
      color: [...channelConfig.map((config) => config.color), '#8B0000'], // 包含所有渠道和深红色趋势线
      grid: {
        left: '4%',
        right: '4%',
        bottom: '15%', // 为底部legend留出更多空间
        containLabel: true,
      },
      legend: {
        data: [...channelConfig.map((config) => config.name), t('totalTrend')], // 包含所有渠道和趋势线
        bottom: '5%', // legend放在底部
        left: 'center',
        type: 'scroll', // 如果legend项太多，支持滚动
      },
      xAxis: {
        type: 'category',
        data: data.map((item) => item.name),
        axisLabel: {
          interval: 0, // 强制显示所有标签
          rotate: 30, // 倾斜30度
        },
      },
      yAxis: {
        type: 'value',
        name: t('numberOfClues'),
      },
      series: series as any,
      tooltip: {
        show: true,
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: formatUtmTooltip, // 使用UTM专用的tooltip函数
      },
    },
    size: { height: 450 },
    position: { x: 0, y: 0 },
  };
};

/** UTM图表配置（默认使用utm_source数据） */
export const utmChartConfigs: ChartConfig[] = [generateUtmChartConfig('utmSource')];

/**
 * 线索有效性数据（兼容ChartDataItem接口）- 7天数据
 * 使用ChartDataItem标准结构，额外属性存储在扩展字段中
 */
export const clueEffectivenessData: ChartDataItem[] = [
  {
    name: '2025-07-24',
    value: 120, // 线索总数作为主value
    type: 'period',
    totalClues: 120,
    effectiveClues: 90,
    effectivenessRate: 75.0,
  },
  {
    name: '2025-07-25',
    value: 135,
    type: 'period',
    totalClues: 135,
    effectiveClues: 105,
    effectivenessRate: 77.8,
  },
  {
    name: '2025-07-26',
    value: 150,
    type: 'period',
    totalClues: 150,
    effectiveClues: 118,
    effectivenessRate: 78.7,
  },
  {
    name: '2025-07-27',
    value: 110,
    type: 'period',
    totalClues: 110,
    effectiveClues: 85,
    effectivenessRate: 77.3,
  },
  {
    name: '2025-07-28',
    value: 140,
    type: 'period',
    totalClues: 140,
    effectiveClues: 105,
    effectivenessRate: 75.0,
  },
  {
    name: '2025-07-29',
    value: 165,
    type: 'period',
    totalClues: 165,
    effectiveClues: 120,
    effectivenessRate: 72.7,
  },
  {
    name: '2025-07-30',
    value: 175,
    type: 'period',
    totalClues: 175,
    effectiveClues: 120,
    effectivenessRate: 68.6,
  },
];

/**
 * 线索有效性tooltip格式化函数
 */
const formatEffectivenessTooltip = createTooltipFormatter({
  extraInfoProvider: (axisValue: string) => {
    // 查找对应日期的数据来获取有效率
    const dayData = clueEffectivenessData.find((item: any) => item.name === axisValue);
    const effectivenessRate = dayData?.effectivenessRate || 0;
    return `${t('efficient')}: ${effectivenessRate}%`;
  },
});

/**
 * 生成线索有效性折线图配置
 */
export const generateClueEffectivenessChartConfig = (): ChartConfig => {
  const data = clueEffectivenessData;

  // 生成线索总量系列
  const totalCluesSeries = {
    name: t('totalNumberOfLeads'),
    type: 'line',
    data: data.map((item: any) => ({
      name: item.name,
      value: item.totalClues, // 使用totalClues字段
    })),
    itemStyle: {
      color: '#5470c6', // 蓝色
    },
    lineStyle: {
      color: '#5470c6',
      width: 2,
    },
    symbol: 'circle',
    symbolSize: 6,
  };

  // 生成有效线索总量系列
  const effectiveCluesSeries = {
    name: t('totalNumberOfEffectiveClues'),
    type: 'line',
    data: data.map((item: any) => ({
      name: item.name,
      value: item.effectiveClues, // 使用effectiveClues字段
    })),
    itemStyle: {
      color: '#91cc75', // 绿色
    },
    lineStyle: {
      color: '#91cc75',
      width: 2,
    },
    symbol: 'circle',
    symbolSize: 6,
  };

  return {
    id: 'clueEffectiveness',
    type: 'line',
    title: t('clueValidity'),
    dataSource: 'clueEffectiveness',
    options: {
      // 禁用ECharts内置标题，使用自定义标题
      title: {
        show: false,
      },
      color: ['#5470c6', '#91cc75'], // 蓝色和绿色
      grid: {
        left: '4%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
      },
      legend: {
        data: [t('totalNumberOfLeads'), t('totalNumberOfEffectiveClues')],
        bottom: '5%',
        left: 'center',
      },
      xAxis: {
        type: 'category',
        data: data.map((item) => item.name),
        axisLabel: {
          interval: 0, // 强制显示所有标签
          rotate: 30, // 倾斜30度
        },
      },
      yAxis: {
        type: 'value',
        name: t('numberOfClues'),
        min: 0,
        max: 180,
      },
      series: [totalCluesSeries, effectiveCluesSeries] as any,
      tooltip: {
        show: true,
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
        },
        formatter: formatEffectivenessTooltip, // 使用专用的tooltip格式化函数
      },
    },
    size: { height: 450 },
    position: { x: 0, y: 0 },
  };
};

/** 线索有效性图表配置 */
export const clueEffectivenessChartConfigs: ChartConfig[] = [generateClueEffectivenessChartConfig()];
