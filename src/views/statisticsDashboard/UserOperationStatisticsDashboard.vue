<template>
  <div class="statistics-dashboard">
    <PageWrapper>
      <!-- <template #extra>
        <div class="flex items-center gap-2">
          <PerformanceMonitor />
          <a-button type="primary" @click="handleRefreshAll">
            <Icon icon="ant-design:reload-outlined" />
            刷新全部
          </a-button>
        </div>
      </template> -->

      <!-- 筛选器面板 -->
      <!-- <FilterPanel :auto-apply="false" @filter-apply="handleFilterApply" @filter-change="handleFilterChange" @filter-clear="handleFilterClear" /> -->

      <!-- Tab容器 -->
      <TabContainer v-model="activeTab" :tabs="tabs" :loading="loading" @tab-change="handleTabChange" />

      <!-- 浮动操作按钮 -->
      <div class="floating-actions">
        <a-float-button-group trigger="click" type="primary">
          <template #icon>
            <Icon icon="ant-design:setting-outlined" />
          </template>

          <a-float-button tooltip="刷新所有数据" @click="handleRefreshAll">
            <template #icon>
              <Icon icon="ant-design:reload-outlined" />
            </template>
          </a-float-button>

          <a-float-button tooltip="导出当前看板" @click="handleExportDashboard">
            <template #icon>
              <Icon icon="ant-design:download-outlined" />
            </template>
          </a-float-button>

          <a-float-button tooltip="看板设置" @click="handleDashboardSettings">
            <template #icon>
              <Icon icon="ant-design:control-outlined" />
            </template>
          </a-float-button>
        </a-float-button-group>
      </div>

      <!-- 设置抽屉 -->
      <a-drawer v-model:open="settingsVisible" title="看板设置" placement="right" width="400">
        <div class="settings-content">
          <a-form layout="vertical">
            <a-form-item label="显示设置">
              <a-space direction="vertical" style="width: 100%">
                <a-switch v-model:checked="isDraggingEnabled" checked-children="启用拖拽" un-checked-children="禁用拖拽" />
              </a-space>
            </a-form-item>
            <a-form-item label="主题设置">
              <a-radio-group v-model:value="theme">
                <a-radio value="light">浅色主题</a-radio>
                <a-radio value="dark">深色主题</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-form>
        </div>
      </a-drawer>
    </PageWrapper>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';
  import { PageWrapper } from '/@/components/Page';
  import { Icon } from '/@/components/Icon';
  // import FilterPanel from './components/FilterPanel.vue';
  import TabContainer from './components/TabContainer.vue';
  // import PerformanceMonitor from './components/PerformanceMonitor.vue';
  import { useStatisticDashboard } from './hooks/useStatisticDashboard';
  import { useChartData } from './hooks/useChartData';
  import { provideChartActions, provideChartData, provideChartEvents, provideChartState, provideChartConfig } from './hooks/useChartActions';
  import type { TabConfig } from './types/statisticDashboard';
  import { message } from 'ant-design-vue';
  import { queryAllClueSource } from './api';
  import { StatisticalDimensionTypeEnum } from './enums';
  import { useUserStore } from '/@/store/modules/user';
  // import { queryAllClueSource } from './api';
  // import { StatisticalDimensionTypeEnum } from './enums';

  // 使用状态管理Hook
  const { activeTab, tabs, currentCharts, switchTab } = useStatisticDashboard();

  // 使用图表数据Hook
  const { clearCache } = useChartData();

  // 本地状态
  const settingsVisible = ref(false);
  const isDraggingEnabled = ref(false);
  const theme = ref('light');

  // 提供所有图表上下文 - 使用新的配置管理系统
  const chartActions = provideChartActions();
  provideChartData(chartActions); // 🔥 修复：传入chartActions
  provideChartEvents(chartActions.context.handleDrillDown);
  const { loading } = provideChartState(false);
  provideChartConfig();

  const { userInfo } = useUserStore();

  // 测试数据
  queryAllClueSource({
    startDate: '2025-07-31',
    endDate: '2025-07-31',
    statisticalDimension: StatisticalDimensionTypeEnum.DAY,
    regionCenterCode: userInfo!.regionCenterCode,
  }).then((res) => {
    console.log('res', res);
  });

  /**
   * 处理筛选条件应用
   */
  // const handleFilterApply = (filters: FilterConfig) => {
  //   console.log('应用筛选条件:', filters);
  //   // updateFilters(filters);

  //   // 重新获取所有图表数据
  //   handleRefreshAll();

  //   message.success('筛选条件已应用');
  // };

  /**
   * 处理筛选条件变化
   */
  // const handleFilterChange = (filters: FilterConfig) => {
  //   console.log('筛选条件变化:', filters);
  //   // todo 需要调研如何把筛选参数传递给chartGroupContainer组件
  // };

  /**
   * 处理筛选条件清除
   */
  // const handleFilterClear = () => {
  //   console.log('清除筛选条件');
  //   handleRefreshAll();
  // };

  /**
   * 处理Tab切换 - 只处理业务逻辑，Tab状态由上下文系统管理
   */
  const handleTabChange = (tabId: string, tab: TabConfig) => {
    console.log(`切换到Tab: ${tab.name}`);
    switchTab(tabId);
    // 注意：Tab状态已由TabContainer通过上下文系统设置，这里不需要重复设置
  };

  // 注意：图表刷新现在统一由上下文系统处理，不再需要本地的handleRefreshChart函数

  /**
   * 刷新所有数据 - 使用上下文系统统一处理
   */
  const handleRefreshAll = async () => {
    loading.value = true;

    try {
      // 清除缓存
      clearCache();

      // 使用上下文系统刷新当前Tab的所有图表
      let refreshCount = 0;
      if (currentCharts.value.length > 0) {
        for (const chart of currentCharts.value) {
          chartActions.context.refreshChart(chart.id);
          refreshCount++;
        }
      }

      message.success(`所有数据已刷新 (${refreshCount}个图表)`);
    } catch (error) {
      console.error('刷新失败:', error);
      message.error('刷新失败');
    } finally {
      loading.value = false;
    }
  };

  // 注意：Tab级别的导出、全屏功能现在由TabContainer内部处理，不再需要这些函数

  /**
   * 处理看板导出
   */
  const handleExportDashboard = () => {
    message.info('正在导出整个看板...');
    // 这里实现看板导出逻辑
  };

  /**
   * 处理看板设置
   */
  const handleDashboardSettings = () => {
    settingsVisible.value = true;
  };

  // 组件挂载
  onMounted(() => {
    // 初始化数据
    handleRefreshAll();
  });

  // 组件卸载
  onUnmounted(() => {});
</script>

<style lang="less" scoped>
  .statistics-dashboard {
    min-height: 100vh;
    background: #f5f5f5;

    .chart-item {
      height: 100%;
    }

    .floating-actions {
      position: fixed;
      bottom: 24px;
      right: 24px;
      z-index: 100;
    }

    .settings-content {
      .ant-form-item {
        margin-bottom: 24px;
      }
    }
  }

  // 移除了响应式设计相关的媒体查询
</style>
