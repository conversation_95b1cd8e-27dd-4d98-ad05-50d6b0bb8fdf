/**
 * API图表配置管理Hook
 * 专门负责将mock配置渐进式迁移到API驱动的配置生成
 */

import { reactive, readonly } from 'vue';
import type { ChartConfig, ChartDataItem } from '../types/statisticDashboard';
import { message } from 'ant-design-vue';

// 导入数据源管理器和筛选器
import { useDataSourceManager } from './useDataSourceManager';
import { useFilters } from './useFilters';

// 导入原始mock配置作为fallback
import { sourceOfCluesChartConfigs } from '../mock/clueSourceMock';

/**
 * 图表API迁移状态枚举
 */
export enum ChartMigrationStatus {
  /** 使用Mock配置 */
  MOCK = 'mock',
  /** 使用API配置 */
  API = 'api',
  /** 混合模式（API优先，Mock fallback） */
  HYBRID = 'hybrid',
}

/**
 * 图表配置迁移设置
 */
interface ChartMigrationConfig {
  /** 图表ID */
  chartId: string;
  /** 迁移状态 */
  status: ChartMigrationStatus;
  /** 是否启用 */
  enabled: boolean;
  /** 最后更新时间 */
  lastUpdated: Date;
  /** 错误信息 */
  error?: string;
}

/**
 * API图表配置管理Hook
 */
export function useAPIChartConfig() {
  // 数据源管理器
  const dataSourceManager = useDataSourceManager();

  // 筛选器（需要外部传入参数获取函数）
  let filtersHook: ReturnType<typeof useFilters> | null = null;

  // 迁移配置状态
  const migrationConfigs = reactive<Record<string, ChartMigrationConfig>>({
    sourceOfClues: {
      chartId: 'sourceOfClues',
      status: ChartMigrationStatus.HYBRID, // 默认混合模式
      enabled: true,
      lastUpdated: new Date(),
    },
  });

  // 加载状态
  const loadingStates = reactive<Record<string, boolean>>({});

  // 错误状态
  const errorStates = reactive<Record<string, string | null>>({});

  // 生成的API配置缓存
  const apiConfigCache = reactive<Record<string, ChartConfig>>({});

  // 配置状态管理
  const configStates = reactive<
    Record<
      string,
      {
        lastFetchTime: Date | null;
        fetchCount: number;
        successCount: number;
        errorCount: number;
        avgResponseTime: number;
        isHealthy: boolean;
      }
    >
  >({
    sourceOfClues: {
      lastFetchTime: null,
      fetchCount: 0,
      successCount: 0,
      errorCount: 0,
      avgResponseTime: 0,
      isHealthy: true,
    },
  });

  /**
   * 设置筛选器Hook
   */
  const setFiltersHook = (filters: ReturnType<typeof useFilters>) => {
    filtersHook = filters;
  };

  /**
   * 设置图表迁移状态
   */
  const setChartMigrationStatus = (chartId: string, status: ChartMigrationStatus) => {
    if (migrationConfigs[chartId]) {
      migrationConfigs[chartId].status = status;
      migrationConfigs[chartId].lastUpdated = new Date();
      migrationConfigs[chartId].error = undefined;

      console.log(`图表 ${chartId} 迁移状态已设置为: ${status}`);
    }
  };

  /**
   * 启用/禁用图表API迁移
   */
  const toggleChartMigration = (chartId: string, enabled: boolean) => {
    if (migrationConfigs[chartId]) {
      migrationConfigs[chartId].enabled = enabled;
      migrationConfigs[chartId].lastUpdated = new Date();

      console.log(`图表 ${chartId} API迁移已${enabled ? '启用' : '禁用'}`);
    }
  };

  /**
   * 从API生成线索来源图表配置
   */
  const generateSourceOfCluesConfigFromAPI = async (
    dataSource: 'sourceOfAllClues' | 'sourceOfEffectiveClues' = 'sourceOfAllClues'
  ): Promise<ChartConfig | null> => {
    const chartId = 'sourceOfClues';

    try {
      loadingStates[chartId] = true;
      errorStates[chartId] = null;

      console.log(`正在从API生成 ${dataSource} 图表配置...`);

      // 检查筛选器是否可用
      if (!filtersHook) {
        throw new Error('筛选器Hook未设置，无法获取API参数');
      }

      // 验证筛选参数
      const validation = filtersHook.validateFilterParams();
      if (!validation.isValid) {
        throw new Error(validation.message || '筛选参数无效');
      }

      // 获取API参数
      const apiParams = filtersHook.getAPIParamsForAllClueSource();
      console.log('API参数:', apiParams);

      // 调用API获取数据
      let apiData: ChartDataItem[];
      if (dataSource === 'sourceOfEffectiveClues') {
        apiData = await dataSourceManager.fetchValidClueSourceFromAPI(apiParams);
      } else {
        apiData = await dataSourceManager.fetchAllClueSourceFromAPI(apiParams);
      }

      if (!apiData || apiData.length === 0) {
        throw new Error('API返回数据为空');
      }

      console.log(`API数据获取成功，数据量: ${apiData.length}`);

      // 生成图表配置
      const config = generateChartConfigFromAPIData(apiData, dataSource);

      // 缓存配置
      const cacheKey = `${chartId}_${dataSource}`;
      apiConfigCache[cacheKey] = config;

      console.log(`${dataSource} 图表配置生成成功`);
      return config;
    } catch (error) {
      console.error(`生成 ${dataSource} API配置失败:`, error);
      errorStates[chartId] = error instanceof Error ? error.message : '生成配置失败';

      // 根据迁移状态决定是否抛出错误
      const migrationConfig = migrationConfigs[chartId];
      if (migrationConfig?.status === ChartMigrationStatus.API) {
        // API模式下抛出错误
        throw error;
      }

      // 混合模式下返回null，让调用方使用fallback
      return null;
    } finally {
      loadingStates[chartId] = false;
    }
  };

  /**
   * 从API数据生成图表配置
   */
  const generateChartConfigFromAPIData = (apiData: ChartDataItem[], dataSource: 'sourceOfAllClues' | 'sourceOfEffectiveClues'): ChartConfig => {
    const isAllClues = dataSource === 'sourceOfAllClues';
    const title = isAllClues ? '全量线索来源' : '有效线索来源';

    return {
      id: 'sourceOfClues',
      title,
      type: 'pie',
      dataSource,

      // 基于API数据的ECharts配置
      options: {
        title: {
          text: title,
          left: 'center',
          top: '5%',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
          },
        },
        tooltip: {
          trigger: 'item',
          formatter: (params: any) => {
            const data = params.data;
            return `${data.name}<br/>数量: ${data.value}<br/>占比: ${data.percentage}%`;
          },
        },
        legend: {
          data: apiData.map((item) => item.name),
          bottom: '5%',
          left: 'center',
        },
        series: [
          {
            name: title,
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '50%'],
            data: apiData.map((item) => ({
              name: item.name,
              value: item.value,
              itemStyle: {
                color: item.color || '#5470c6',
              },
              // 保留原始数据用于下探
              _rawData: item,
            })),
            label: {
              show: true,
              formatter: (params: any) => {
                const rawData = params.data._rawData;
                return `${params.name}\n${params.value} (${rawData?.percentage || 0}%)`;
              },
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      },

      // 自定义属性
      customProps: {
        currentDataSource: dataSource,
        drillDownLevel: 0,
        isDrillDown: false,
        switchable: true,
        alternativeDataSource: isAllClues ? 'sourceOfEffectiveClues' : 'sourceOfAllClues',
        alternativeTitle: isAllClues ? '有效线索来源' : '全量线索来源',
        isFromAPI: true, // 标识来自API
        apiData, // 保存原始API数据
      },

      // 下探配置
      drillDown: {
        enabled: true,
        maxLevel: 1,
        currentLevel: 0,
        dataStrategy: 'async',
        levels: [
          {
            level: 0,
            dataKey: 'oneSource',
            titleField: 'name',
            valueField: 'value',
          },
          {
            level: 1,
            dataKey: 'twoSource',
            titleField: 'name',
            valueField: 'value',
          },
        ],
      },

      // 布局配置
      position: { x: 0, y: 0 },
    };
  };

  /**
   * 获取图表配置（支持渐进式迁移）
   */
  const getChartConfig = async (chartId: string, dataSource?: 'sourceOfAllClues' | 'sourceOfEffectiveClues'): Promise<ChartConfig[]> => {
    const migrationConfig = migrationConfigs[chartId];

    if (!migrationConfig || !migrationConfig.enabled) {
      // 未启用迁移，使用原始mock配置
      console.log(`图表 ${chartId} 未启用API迁移，使用Mock配置`);
      return getOriginalMockConfig(chartId);
    }

    try {
      switch (migrationConfig.status) {
        case ChartMigrationStatus.API:
          // 纯API模式
          console.log(`图表 ${chartId} 使用纯API模式`);
          const apiConfig = await generateSourceOfCluesConfigFromAPI(dataSource);
          if (!apiConfig) {
            throw new Error('API配置生成失败');
          }
          return [apiConfig];

        case ChartMigrationStatus.HYBRID:
          // 混合模式：API优先，Mock fallback
          console.log(`图表 ${chartId} 使用混合模式`);
          const hybridConfig = await generateSourceOfCluesConfigFromAPI(dataSource);
          if (hybridConfig) {
            return [hybridConfig];
          } else {
            console.log(`API配置生成失败，回退到Mock配置`);
            message.warning('使用默认配置，数据可能不是最新的');
            return getOriginalMockConfig(chartId);
          }

        case ChartMigrationStatus.MOCK:
        default:
          // Mock模式
          console.log(`图表 ${chartId} 使用Mock模式`);
          return getOriginalMockConfig(chartId);
      }
    } catch (error) {
      console.error(`获取图表配置失败:`, error);

      // 错误时根据模式决定处理方式
      if (migrationConfig.status === ChartMigrationStatus.API) {
        message.error('获取图表配置失败');
        throw error;
      } else {
        message.warning('获取最新配置失败，使用默认配置');
        return getOriginalMockConfig(chartId);
      }
    }
  };

  /**
   * 获取原始Mock配置
   */
  const getOriginalMockConfig = (chartId: string): ChartConfig[] => {
    switch (chartId) {
      case 'sourceOfClues':
        return [...sourceOfCluesChartConfigs]; // 返回副本
      default:
        console.warn(`未知的图表ID: ${chartId}`);
        return [];
    }
  };

  /**
   * 刷新API配置
   */
  const refreshAPIConfig = async (chartId: string, dataSource?: string) => {
    console.log(`刷新图表 ${chartId} 的API配置`);

    // 清除缓存
    Object.keys(apiConfigCache).forEach((key) => {
      if (key.startsWith(chartId)) {
        delete apiConfigCache[key];
      }
    });

    // 重新生成配置
    return await getChartConfig(chartId, dataSource as any);
  };

  /**
   * 批量设置图表迁移状态
   */
  const batchSetMigrationStatus = (configs: Array<{ chartId: string; status: ChartMigrationStatus; enabled?: boolean }>) => {
    configs.forEach(({ chartId, status, enabled = true }) => {
      if (migrationConfigs[chartId]) {
        migrationConfigs[chartId].status = status;
        migrationConfigs[chartId].enabled = enabled;
        migrationConfigs[chartId].lastUpdated = new Date();
        migrationConfigs[chartId].error = undefined;
      }
    });

    console.log('批量设置迁移状态完成:', configs);
  };

  /**
   * 获取迁移状态统计
   */
  const getMigrationStats = () => {
    const stats = {
      total: 0,
      mock: 0,
      api: 0,
      hybrid: 0,
      enabled: 0,
      disabled: 0,
      errors: 0,
    };

    Object.values(migrationConfigs).forEach((config) => {
      stats.total++;

      if (config.enabled) {
        stats.enabled++;
      } else {
        stats.disabled++;
      }

      if (config.error) {
        stats.errors++;
      }

      switch (config.status) {
        case ChartMigrationStatus.MOCK:
          stats.mock++;
          break;
        case ChartMigrationStatus.API:
          stats.api++;
          break;
        case ChartMigrationStatus.HYBRID:
          stats.hybrid++;
          break;
      }
    });

    return stats;
  };

  /**
   * 渐进式迁移控制器
   */
  const migrationController = {
    /**
     * 启用所有图表的混合模式（推荐的第一步）
     */
    enableHybridMode: () => {
      batchSetMigrationStatus([{ chartId: 'sourceOfClues', status: ChartMigrationStatus.HYBRID, enabled: true }]);
      message.success('已启用混合模式，API优先，Mock fallback');
    },

    /**
     * 切换到纯API模式（确认API稳定后）
     */
    switchToAPIMode: () => {
      batchSetMigrationStatus([{ chartId: 'sourceOfClues', status: ChartMigrationStatus.API, enabled: true }]);
      message.success('已切换到纯API模式');
    },

    /**
     * 回退到Mock模式（紧急情况）
     */
    fallbackToMockMode: () => {
      batchSetMigrationStatus([{ chartId: 'sourceOfClues', status: ChartMigrationStatus.MOCK, enabled: true }]);
      message.warning('已回退到Mock模式');
    },

    /**
     * 禁用所有API迁移
     */
    disableAllMigration: () => {
      Object.keys(migrationConfigs).forEach((chartId) => {
        toggleChartMigration(chartId, false);
      });
      message.info('已禁用所有API迁移');
    },

    /**
     * 获取当前迁移状态
     */
    getStatus: () => {
      const stats = getMigrationStats();
      console.log('迁移状态统计:', stats);
      return {
        stats,
        configs: { ...migrationConfigs },
        recommendations: generateRecommendations(stats),
      };
    },
  };

  /**
   * 生成迁移建议
   */
  const generateRecommendations = (stats: ReturnType<typeof getMigrationStats>) => {
    const recommendations: string[] = [];

    if (stats.errors > 0) {
      recommendations.push('存在配置错误，建议检查API连接和参数');
    }

    if (stats.mock === stats.total) {
      recommendations.push('所有图表使用Mock模式，建议先启用混合模式进行测试');
    }

    if (stats.hybrid > 0 && stats.errors === 0) {
      recommendations.push('混合模式运行正常，可以考虑切换到纯API模式');
    }

    if (stats.api === stats.total && stats.errors === 0) {
      recommendations.push('所有图表已成功迁移到API模式');
    }

    return recommendations;
  };

  return {
    // 状态（只读）
    migrationConfigs: readonly(migrationConfigs),
    loadingStates: readonly(loadingStates),
    errorStates: readonly(errorStates),
    apiConfigCache: readonly(apiConfigCache),
    configStates: readonly(configStates),

    // 配置方法
    setFiltersHook,
    setChartMigrationStatus,
    toggleChartMigration,
    batchSetMigrationStatus,

    // 核心方法
    getChartConfig,
    refreshAPIConfig,
    generateSourceOfCluesConfigFromAPI,

    // 工具方法
    getOriginalMockConfig,
    getMigrationStats,

    // 渐进式迁移控制器
    migrationController,

    // 枚举导出
    ChartMigrationStatus,
  };
}
