/**
 * 数据源管理Hook
 * 统一管理数据源映射和数据获取逻辑，支持从真实API获取线索来源数据
 */

import { reactive, computed, readonly, ref } from 'vue';
import type { ChartDataItem } from '../types/statisticDashboard';
import { message } from 'ant-design-vue';

// 导入API接口
import {
  queryAllClueSource,
  queryValidClueSource,
  queryAllClueSourceSecond,
  queryValidClueSourceSecond,
  type QueryAllClueSourceParams,
  type QueryAllClueSourceSecondParams,
  type AllClueSourceResponseType
} from '../api/index';

// 导入具体的数据源（作为fallback）
import { sourceOfAllCluesData, sourceOfEffectiveCluesData, utmSourceData, utmMediumData, clueEffectivenessData } from '../mock/clueSourceMock';
import { followUpOnCluesData, firstFollowUpTimeAnalysisData, firstResponseTimeoutData } from '../mock/followUpOnCluesMock';

// 导入数据转换工具函数
import {
  transformOneSourceDataToChartData,
  transformTwoSourceDataToChartData,
  validateAPIResponse,
  createFallbackChartData
} from '../utils/index';

/**
 * 数据源管理Hook
 */
export function useDataSourceManager() {
  // 数据源映射存储
  const dataSourceMap = reactive<Record<string, ChartDataItem[]>>({});

  // 数据源元数据
  const dataSourceMeta = reactive<
    Record<
      string,
      {
        name: string;
        description: string;
        category: string;
        lastUpdated: Date;
        size: number;
        isFromAPI?: boolean; // 标识是否来自API
      }
    >
  >({});

  // 加载状态管理
  const loadingStates = reactive<Record<string, boolean>>({});

  // 错误状态管理
  const errorStates = reactive<Record<string, string | null>>({});

  /**
   * 从API获取全量线索来源数据
   */
  const fetchAllClueSourceFromAPI = async (params: QueryAllClueSourceParams): Promise<ChartDataItem[]> => {
    const dataSourceKey = 'sourceOfAllClues';

    try {
      loadingStates[dataSourceKey] = true;
      errorStates[dataSourceKey] = null;

      console.log('正在获取全量线索来源数据，参数：', params);
      const response = await queryAllClueSource(params);

      if (response && response.success) {
        const chartData = transformOneSourceDataToChartData(response.result);

        // 更新数据源
        setDataSource(dataSourceKey, chartData, {
          name: '全量线索来源',
          description: '从API获取的全量线索来源分布数据',
          category: 'clue',
          isFromAPI: true,
        });

        console.log('全量线索来源数据获取成功，数据长度：', chartData.length);
        return chartData;
      } else {
        throw new Error(response?.message || '获取数据失败');
      }
    } catch (error) {
      console.error('获取全量线索来源数据失败：', error);
      errorStates[dataSourceKey] = error instanceof Error ? error.message : '获取数据失败';
      message.error('获取全量线索来源数据失败，使用默认数据');

      // 返回默认数据
      return getDataSource(dataSourceKey) || sourceOfAllCluesData;
    } finally {
      loadingStates[dataSourceKey] = false;
    }
  };

  /**
   * 从API获取有效线索来源数据
   */
  const fetchValidClueSourceFromAPI = async (params: QueryAllClueSourceParams): Promise<ChartDataItem[]> => {
    const dataSourceKey = 'sourceOfEffectiveClues';

    try {
      loadingStates[dataSourceKey] = true;
      errorStates[dataSourceKey] = null;

      console.log('正在获取有效线索来源数据，参数：', params);
      const response = await queryValidClueSource(params);

      if (response && response.success) {
        const chartData = transformOneSourceDataToChartData(response.result);

        // 更新数据源
        setDataSource(dataSourceKey, chartData, {
          name: '有效线索来源',
          description: '从API获取的有效线索来源分布数据',
          category: 'clue',
          isFromAPI: true,
        });

        console.log('有效线索来源数据获取成功，数据长度：', chartData.length);
        return chartData;
      } else {
        throw new Error(response?.message || '获取数据失败');
      }
    } catch (error) {
      console.error('获取有效线索来源数据失败：', error);
      errorStates[dataSourceKey] = error instanceof Error ? error.message : '获取数据失败';
      message.error('获取有效线索来源数据失败，使用默认数据');

      // 返回默认数据
      return getDataSource(dataSourceKey) || sourceOfEffectiveCluesData;
    } finally {
      loadingStates[dataSourceKey] = false;
    }
  };

  /**
   * 从API获取二级线索来源数据（下探功能）
   */
  const fetchSecondLevelClueSourceFromAPI = async (
    params: QueryAllClueSourceSecondParams,
    isValid: boolean = false
  ): Promise<ChartDataItem[]> => {
    const dataSourceKey = `${isValid ? 'valid' : 'all'}ClueSecondLevel_${params.oneSourceId}`;

    try {
      loadingStates[dataSourceKey] = true;
      errorStates[dataSourceKey] = null;

      console.log(`正在获取${isValid ? '有效' : '全量'}线索二级来源数据，参数：`, params);
      const response = isValid
        ? await queryValidClueSourceSecond(params)
        : await queryAllClueSourceSecond(params);

      if (response && response.success) {
        const chartData = transformTwoSourceDataToChartData(response.result);

        // 更新数据源
        setDataSource(dataSourceKey, chartData, {
          name: `${isValid ? '有效' : '全量'}线索二级来源`,
          description: `从API获取的${isValid ? '有效' : '全量'}线索二级来源分布数据`,
          category: 'clue',
          isFromAPI: true,
        });

        console.log(`${isValid ? '有效' : '全量'}线索二级来源数据获取成功，数据长度：`, chartData.length);
        return chartData;
      } else {
        throw new Error(response?.message || '获取数据失败');
      }
    } catch (error) {
      console.error(`获取${isValid ? '有效' : '全量'}线索二级来源数据失败：`, error);
      errorStates[dataSourceKey] = error instanceof Error ? error.message : '获取数据失败';
      message.error(`获取${isValid ? '有效' : '全量'}线索二级来源数据失败`);

      // 返回空数组，让调用方处理
      return [];
    } finally {
      loadingStates[dataSourceKey] = false;
    }
  };

  /**
   * 初始化默认数据源
   */
  const initializeDefaultDataSources = () => {
    const defaultDataSources = {
      // 线索相关数据源
      sourceOfAllClues: {
        data: sourceOfAllCluesData,
        meta: {
          name: '全量线索来源',
          description: '所有线索的来源分布数据',
          category: 'clue',
          lastUpdated: new Date(),
          size: sourceOfAllCluesData.length,
        },
      },
      sourceOfEffectiveClues: {
        data: sourceOfEffectiveCluesData,
        meta: {
          name: '有效线索来源',
          description: '有效线索的来源分布数据',
          category: 'clue',
          lastUpdated: new Date(),
          size: sourceOfEffectiveCluesData.length,
        },
      },

      // UTM相关数据源
      utmSource: {
        data: utmSourceData,
        meta: {
          name: 'UTM来源',
          description: 'UTM来源统计数据',
          category: 'utm',
          lastUpdated: new Date(),
          size: utmSourceData.length,
        },
      },
      utmMedium: {
        data: utmMediumData,
        meta: {
          name: 'UTM媒介',
          description: 'UTM媒介统计数据',
          category: 'utm',
          lastUpdated: new Date(),
          size: utmMediumData.length,
        },
      },

      // 线索效果数据源
      clueEffectiveness: {
        data: clueEffectivenessData,
        meta: {
          name: '线索有效性',
          description: '线索有效性分析数据',
          category: 'effectiveness',
          lastUpdated: new Date(),
          size: clueEffectivenessData.length,
        },
      },

      // 跟进相关数据源
      followUpOnClues: {
        data: followUpOnCluesData,
        meta: {
          name: '线索跟进状态',
          description: '线索跟进状态统计数据',
          category: 'followUp',
          lastUpdated: new Date(),
          size: followUpOnCluesData.length,
        },
      },
      firstFollowUpTimeAnalysis: {
        data: firstFollowUpTimeAnalysisData,
        meta: {
          name: '首次跟进时长分析',
          description: '首次跟进时长分析数据',
          category: 'followUp',
          lastUpdated: new Date(),
          size: firstFollowUpTimeAnalysisData.length,
        },
      },
      firstResponseTimeout: {
        data: firstResponseTimeoutData,
        meta: {
          name: '首次响应超时',
          description: '首次响应超时统计数据',
          category: 'followUp',
          lastUpdated: new Date(),
          size: firstResponseTimeoutData.length,
        },
      },
    };

    // 注册所有数据源
    Object.entries(defaultDataSources).forEach(([key, { data, meta }]) => {
      dataSourceMap[key] = data;
      dataSourceMeta[key] = meta;
    });
  };

  /**
   * 获取数据源数据
   */
  const getDataSource = (dataSourceKey: string): ChartDataItem[] => {
    return dataSourceMap[dataSourceKey] || [];
  };

  /**
   * 设置数据源数据
   */
  const setDataSource = (dataSourceKey: string, data: ChartDataItem[], meta?: Partial<(typeof dataSourceMeta)[string]>) => {
    dataSourceMap[dataSourceKey] = data;

    if (meta) {
      dataSourceMeta[dataSourceKey] = {
        name: meta.name || dataSourceKey,
        description: meta.description || '',
        category: meta.category || 'custom',
        lastUpdated: new Date(),
        size: data.length,
        ...meta,
      };
    }
  };

  /**
   * 更新数据源数据
   */
  const updateDataSource = (dataSourceKey: string, data: ChartDataItem[]) => {
    if (dataSourceMap[dataSourceKey]) {
      dataSourceMap[dataSourceKey] = data;

      if (dataSourceMeta[dataSourceKey]) {
        dataSourceMeta[dataSourceKey].lastUpdated = new Date();
        dataSourceMeta[dataSourceKey].size = data.length;
      }
    }
  };

  /**
   * 删除数据源
   */
  const removeDataSource = (dataSourceKey: string) => {
    delete dataSourceMap[dataSourceKey];
    delete dataSourceMeta[dataSourceKey];
  };

  /**
   * 获取数据源元数据
   */
  const getDataSourceMeta = (dataSourceKey: string) => {
    return dataSourceMeta[dataSourceKey];
  };

  /**
   * 获取所有数据源键名
   */
  const getAllDataSourceKeys = (): string[] => {
    return Object.keys(dataSourceMap);
  };

  /**
   * 按分类获取数据源
   */
  const getDataSourcesByCategory = (category: string): Record<string, ChartDataItem[]> => {
    const result: Record<string, ChartDataItem[]> = {};

    Object.entries(dataSourceMeta).forEach(([key, meta]) => {
      if (meta.category === category) {
        result[key] = dataSourceMap[key];
      }
    });

    return result;
  };

  /**
   * 清空所有数据源
   */
  const clearAllDataSources = () => {
    Object.keys(dataSourceMap).forEach((key) => delete dataSourceMap[key]);
    Object.keys(dataSourceMeta).forEach((key) => delete dataSourceMeta[key]);
  };

  /**
   * 重置为默认数据源
   */
  const resetToDefaults = () => {
    clearAllDataSources();
    initializeDefaultDataSources();
  };

  // 计算属性：数据源统计
  const dataSourceStats = computed(() => {
    const categories = new Set(Object.values(dataSourceMeta).map((meta) => meta.category));
    const totalSize = Object.values(dataSourceMeta).reduce((sum, meta) => sum + meta.size, 0);

    return {
      totalDataSources: Object.keys(dataSourceMap).length,
      totalCategories: categories.size,
      totalDataPoints: totalSize,
      categories: Array.from(categories),
    };
  });

  // 计算属性：按分类分组的数据源
  const dataSourcesByCategory = computed(() => {
    const grouped: Record<string, string[]> = {};

    Object.entries(dataSourceMeta).forEach(([key, meta]) => {
      if (!grouped[meta.category]) {
        grouped[meta.category] = [];
      }
      grouped[meta.category].push(key);
    });

    return grouped;
  });

  // 初始化默认数据源
  initializeDefaultDataSources();

  return {
    // 状态（只读）
    dataSourceMap: readonly(dataSourceMap),
    dataSourceMeta: readonly(dataSourceMeta),
    dataSourceStats,
    dataSourcesByCategory,
    loadingStates: readonly(loadingStates),
    errorStates: readonly(errorStates),

    // 基础方法
    getDataSource,
    setDataSource,
    updateDataSource,
    removeDataSource,
    getDataSourceMeta,
    getAllDataSourceKeys,
    getDataSourcesByCategory,
    clearAllDataSources,
    resetToDefaults,

    // API调用方法
    fetchAllClueSourceFromAPI,
    fetchValidClueSourceFromAPI,
    fetchSecondLevelClueSourceFromAPI,
  };
}
