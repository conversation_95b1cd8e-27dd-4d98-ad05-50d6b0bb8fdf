/**
 * 图表上下文管理 Hook
 * 使用 Provide/Inject 模式避免深层事件传递和数据传递
 */

import { inject, provide, reactive, ref, type InjectionKey } from 'vue';
import type {
  ChartActionContext,
  ChartDataContext,
  ChartEventContext,
  ChartStateContext,
  ChartConfigContext,
  ChartDataItem,
  ChartConfig,
  ChartEventParams,
  TabConfig,
} from '../types/statisticDashboard';
import { message } from 'ant-design-vue';
import { useTabConfigManager } from './useTabConfigManager';
import { useDataSourceManager } from './useDataSourceManager';

// 注入键
export const CHART_ACTION_CONTEXT_KEY: InjectionKey<ChartActionContext> = Symbol('chartActionContext');
export const CHART_DATA_CONTEXT_KEY: InjectionKey<ChartDataContext> = Symbol('chartDataContext');
export const CHART_EVENT_CONTEXT_KEY: InjectionKey<ChartEventContext> = Symbol('chartEventContext');
export const CHART_STATE_CONTEXT_KEY: InjectionKey<ChartStateContext> = Symbol('chartStateContext');
export const CHART_CONFIG_CONTEXT_KEY: InjectionKey<ChartConfigContext> = Symbol('chartConfigContext');

/**
 * 提供图表操作上下文（在顶层组件中使用）
 */
export function provideChartActions() {
  // 使用新的配置管理器
  const tabConfigManager = useTabConfigManager();
  const dataSourceManager = useDataSourceManager();

  // 🔥 整合数据管理到这里，避免inject问题
  const drillDownDataStore = reactive<Record<string, ChartDataItem[]>>({});

  // 内部数据设置函数
  const setInternalChartData = (dataSource: string, data: ChartDataItem[]) => {
    console.log(`setInternalChartData: 设置下探数据源 - ${dataSource}, 数据长度: ${data?.length || 0}`);
    drillDownDataStore[dataSource] = data;
    console.log(`setInternalChartData: 设置完成，当前存储的数据源: ${Object.keys(drillDownDataStore).join(', ')}`);
  };

  // 更新图表配置 - 直接使用配置管理器
  const updateChartConfig = (chartId: string, newConfig: any) => {
    tabConfigManager.updateChartConfig(chartId, newConfig);
  };

  // 获取图表配置 - 直接使用配置管理器
  const getChartConfig = (chartId: string) => {
    return tabConfigManager.getChartConfig(chartId);
  };

  /**
   * 切换图表数据源
   */
  const switchChartDataSource = async (chartId: string, newDataSource: string, newTitle: string) => {
    try {
      if (chartId === 'sourceOfClues') {
        // 获取当前图表配置
        const currentConfig = getChartConfig(chartId);
        const currentDrillLevel = currentConfig?.customProps?.drillDownLevel || 0;
        const drillDownData = currentConfig?.customProps?.drillDownData;
        const parentData = currentConfig?.customProps?.parentData;

        // 动态导入生成函数
        const { generateSourceOfCluesChartConfig } = await import('../mock/clueSourceMock');

        // 生成新配置，保持当前的下探状态
        const newConfig = generateSourceOfCluesChartConfig(newDataSource as any, currentDrillLevel, drillDownData, parentData);

        // 保持下探相关的自定义属性
        const preservedCustomProps = {
          ...newConfig.customProps,
          currentDataSource: newDataSource, // 更新当前数据源
          drillDownLevel: currentDrillLevel,
          drillDownData,
          parentData,
          isDrillDown: currentDrillLevel > 0,
          currentLevel: currentDrillLevel,
        };

        // 如果当前在下探状态，需要保持下探数据源
        let dataSource = newConfig.dataSource;
        if (currentDrillLevel > 0 && currentConfig?.dataSource?.includes('_drill_')) {
          dataSource = currentConfig.dataSource.replace(/(sourceOfAllClues|sourceOfEffectiveClues)/, newDataSource);
        }

        // 更新配置（这会同时更新tabs中的配置）
        updateChartConfig(chartId, {
          ...newConfig,
          dataSource,
          customProps: preservedCustomProps,
          drillDown: {
            ...newConfig.drillDown,
            currentLevel: currentDrillLevel,
          },
        });

        // 触发重新渲染（通过响应式系统自动处理）
        message.success(`已切换到 ${newTitle}`);

        return newConfig;
      } else if (chartId === 'utmChart') {
        // 处理UTM图表的数据源切换
        const { generateUtmChartConfig } = await import('../mock/clueSourceMock');

        // 生成新的UTM图表配置
        const newConfig = generateUtmChartConfig(newDataSource as 'utmSource' | 'utmMedium');

        // 更新配置
        updateChartConfig(chartId, newConfig);

        // 显示成功消息
        message.success(`已切换到 ${newTitle}`);

        return newConfig;
      } else {
        // 其他图表的处理逻辑可以在这里添加
        console.warn(`未知的图表类型: ${chartId}`);
        message.warning(`图表 ${chartId} 暂不支持数据源切换`);
      }
    } catch (error) {
      console.error('切换图表数据源失败:', error);
      message.error('切换失败，请重试');
    }
  };

  /**
   * 刷新图表
   */
  const refreshChart = (chartId: string) => {
    console.log(`刷新图表: ${chartId}`);
    message.success('图表已刷新');
  };

  /**
   * 导出图表
   */
  const exportChart = (chartId: string, format: string) => {
    console.log(`导出图表: ${chartId}, 格式: ${format}`);
    message.info(`正在导出图表为 ${format.toUpperCase()}...`);
  };

  /**
   * 全屏显示图表
   */
  const fullscreenChart = (chartId: string) => {
    console.log(`全屏显示图表: ${chartId}`);
    message.info('全屏功能开发中...');
  };

  /**
   * 通用下探处理函数
   */
  const handleDrillDown = async (data: ChartDataItem, chartConfig: any) => {
    try {
      const drillConfig = chartConfig.drillDown;

      if (!drillConfig || !drillConfig.enabled) {
        message.warning('该图表不支持下探功能');
        return;
      }

      const targetLevel = drillConfig.currentLevel + 1;

      if (targetLevel > drillConfig.maxLevel) {
        message.warning('已达到最大下探层级');
        return;
      }

      let drillDownData: ChartDataItem[];

      // 根据数据策略获取下探数据
      if (drillConfig.dataStrategy === 'async' && drillConfig.dataProvider) {
        // 异步获取数据
        drillDownData = await drillConfig.dataProvider.fetchData(data, targetLevel, chartConfig);
      } else if (drillConfig.dataStrategy === 'hybrid' && drillConfig.dataProvider) {
        // 混合模式：优先异步，回退到静态
        try {
          drillDownData = await drillConfig.dataProvider.fetchData(data, targetLevel, chartConfig);
        } catch (error) {
          console.warn('异步获取失败，回退到静态数据:', error);
          drillDownData = data.children || [];
        }
      } else {
        // 静态数据
        drillDownData = data.children || [];
      }

      if (!drillDownData || drillDownData.length === 0) {
        message.warning(`${data.name} 暂无详细数据`);
        return;
      }

      // 更新图表配置
      await updateChartForDrillDown(chartConfig, drillDownData, data, targetLevel);
    } catch (error) {
      console.error('下探处理失败:', error);
      message.error('下探失败，请重试');
      throw error;
    }
  };

  /**
   * 通用图表配置更新函数 - 🔥 修复：不再使用inject
   */
  const updateChartForDrillDown = async (chartConfig: any, drillDownData: ChartDataItem[], parentData: ChartDataItem, targetLevel: number) => {
    try {
      const drillConfig = chartConfig.drillDown;

      // 创建下探数据源标识
      const drillDownDataSource = `${chartConfig.dataSource}_drill_${targetLevel}_${parentData.channelKey || parentData.name}`;

      console.log(
        `updateChartForDrillDown: 开始更新 - chartId: ${chartConfig.id}, 数据源: ${drillDownDataSource}, 数据长度: ${drillDownData.length}`
      );

      // 🔥 关键修复：直接使用内部数据存储，不再使用inject
      setInternalChartData(drillDownDataSource, drillDownData);
      console.log(`updateChartForDrillDown: 数据设置完成`);

      // 如果是线索来源图表，重新生成下探后的完整配置
      if (chartConfig.id === 'sourceOfClues') {
        const { generateSourceOfCluesChartConfig } = await import('../mock/clueSourceMock');

        // 获取当前的数据源类型（全量或有效）
        const currentDataSourceType = chartConfig.customProps?.currentDataSource || chartConfig.dataSource;

        console.log(`updateChartForDrillDown: 生成下探配置 - 数据源类型: ${currentDataSourceType}, 层级: ${targetLevel}`);

        // 生成下探后的完整配置
        const newDrillConfig = generateSourceOfCluesChartConfig(currentDataSourceType as any, targetLevel, drillDownData, parentData);

        // 🔥 关键修复：确保数据源设置正确
        console.log(`updateChartForDrillDown: 新配置生成完成 - 标题: ${newDrillConfig.title}`);

        // 更新图表配置
        updateChartConfig(chartConfig.id, {
          ...newDrillConfig,
          dataSource: drillDownDataSource, // 🔥 确保使用下探数据源
          drillDown: {
            ...drillConfig,
            currentLevel: targetLevel,
          },
          customProps: {
            ...chartConfig.customProps,
            originalDataSource: chartConfig.dataSource, // 保存原始数据源
            currentDataSource: currentDataSourceType,
            drillDownData,
            parentData,
            isDrillDown: true,
            currentLevel: targetLevel,
            drillDownLevel: targetLevel, // 保持兼容性
          },
        });

        console.log(`updateChartForDrillDown: 配置更新完成`);
        return;
      }

      // 其他类型图表的处理...
      if (drillConfig.dataProvider?.updateChartConfig) {
        const configUpdate = drillConfig.dataProvider.updateChartConfig(drillDownData, targetLevel, chartConfig, parentData);

        updateChartConfig(chartConfig.id, {
          ...chartConfig,
          ...configUpdate,
          dataSource: drillDownDataSource,
          drillDown: {
            ...drillConfig,
            currentLevel: targetLevel,
          },
          customProps: {
            ...chartConfig.customProps,
            originalDataSource: chartConfig.dataSource,
            drillDownData,
            parentData,
            isDrillDown: true,
            currentLevel: targetLevel,
          },
        });
      }
    } catch (error) {
      console.error('更新图表配置失败:', error);
      throw error;
    }
  };

  /**
   * 重置图表到顶层
   */
  const resetChartToTopLevel = async (chartConfig: any) => {
    try {
      const drillConfig = chartConfig.drillDown;

      if (!drillConfig || drillConfig.currentLevel === 0) {
        return; // 已经在顶层了
      }

      console.log(`重置图表到顶层: ${chartConfig.id}, 当前层级: ${drillConfig.currentLevel}`);

      // 恢复原始数据源
      const originalDataSource = chartConfig.customProps?.originalDataSource || chartConfig.dataSource;
      const currentDataSourceType = chartConfig.customProps?.currentDataSource || originalDataSource;

      // 重新生成原始配置
      let originalConfig: Partial<ChartConfig> = {};

      // 如果是线索来源图表，重新生成原始配置
      if (chartConfig.id === 'sourceOfClues') {
        const { generateSourceOfCluesChartConfig } = await import('../mock/clueSourceMock');
        originalConfig = generateSourceOfCluesChartConfig(currentDataSourceType as any, 0); // 顶层
      }

      // 更新图表配置回到顶层
      updateChartConfig(chartConfig.id, {
        ...chartConfig,
        ...originalConfig,
        dataSource: originalDataSource, // 恢复原始数据源
        // 确保保留原始的下探配置，只重置状态
        drillDown: {
          ...chartConfig.drillDown,
          currentLevel: 0,
        },
        customProps: {
          ...chartConfig.customProps,
          currentDataSource: currentDataSourceType, // 保持数据源类型
          originalDataSource: undefined, // 清除原始数据源记录
          drillDownData: undefined,
          parentData: undefined,
          isDrillDown: false,
          currentLevel: 0,
          drillDownLevel: 0, // 重置下探层级
        },
      });

      message.success('已返回顶层');
    } catch (error) {
      console.error('重置到顶层失败:', error);
      message.error('返回顶层失败');
      throw error;
    }
  };

  // 创建上下文对象
  const context: ChartActionContext = {
    switchChartDataSource,
    refreshChart,
    exportChart,
    fullscreenChart,
    // 添加通用下探处理函数
    handleDrillDown,
    // 添加重置到顶层函数
    resetChartToTopLevel,
  };

  // 提供上下文
  provide(CHART_ACTION_CONTEXT_KEY, context);

  return {
    context,
    updateChartConfig,
    getChartConfig,
    // 🔥 新增：暴露内部数据存储给数据上下文使用
    drillDownDataStore,
    setInternalChartData,
    // 暴露配置管理器
    tabConfigManager,
    dataSourceManager,
  };
}

/**
 * 提供图表数据上下文 - 🔥 修复：集成到chartActions的数据存储
 */
export function provideChartData(chartActionsReturn?: ReturnType<typeof provideChartActions>) {
  /**
   * 获取图表数据 - 使用新的数据源管理器
   */
  const getChartData = (dataSource: string): ChartDataItem[] => {
    console.log(`getChartData: 请求数据源 - ${dataSource}`);

    // 🔥 优先检查chartActions的下探数据存储
    if (chartActionsReturn?.drillDownDataStore[dataSource]) {
      console.log(`getChartData: 从下探数据存储获取 - 长度: ${chartActionsReturn.drillDownDataStore[dataSource].length}`);
      return chartActionsReturn.drillDownDataStore[dataSource];
    }

    // 使用数据源管理器获取数据
    if (chartActionsReturn?.dataSourceManager) {
      const result = chartActionsReturn.dataSourceManager.getDataSource(dataSource);
      console.log(`getChartData: 从数据源管理器获取数据 - 长度: ${result?.length || 0}`);
      return result || [];
    }

    // 兜底：返回空数组
    console.warn(`getChartData: 未找到数据源 - ${dataSource}`);
    return [];
  };

  /**
   * 刷新图表数据 - 简化逻辑
   */
  const refreshChartData = async (chartId: string): Promise<void> => {
    // 清除相关的下探数据
    if (chartActionsReturn?.drillDownDataStore) {
      Object.keys(chartActionsReturn.drillDownDataStore).forEach((key) => {
        if (key.includes(chartId)) {
          delete chartActionsReturn.drillDownDataStore[key];
        }
      });
    }
    console.log(`刷新图表数据: ${chartId}`);
  };

  /**
   * 设置图表数据（主要用于下探场景）- 🔥 修复：使用chartActions的数据存储
   */
  const setChartData = (dataSource: string, data: ChartDataItem[]) => {
    console.log(`setChartData: 设置下探数据源 - ${dataSource}, 数据长度: ${data?.length || 0}`);
    if (chartActionsReturn?.setInternalChartData) {
      chartActionsReturn.setInternalChartData(dataSource, data);
    } else {
      console.warn('setChartData: chartActions不可用，无法设置数据');
    }
  };

  /**
   * 清除数据缓存 - 简化为清除下探数据
   */
  const clearDataCache = () => {
    if (chartActionsReturn?.drillDownDataStore) {
      Object.keys(chartActionsReturn.drillDownDataStore).forEach((key) => {
        delete chartActionsReturn.drillDownDataStore[key];
      });
      console.log('clearDataCache: 已清除所有下探数据');
    }
  };

  const dataContext: ChartDataContext = {
    getChartData,
    setChartData,
    refreshChartData,
    clearDataCache,
  };

  provide(CHART_DATA_CONTEXT_KEY, dataContext);

  return dataContext;
}

/**
 * 提供图表事件上下文
 */
export function provideChartEvents(handleDrillDownFn?: (data: ChartDataItem, chartConfig: any) => Promise<void>) {
  /**
   * 图表点击事件
   */
  const onChartClick = (params: ChartEventParams) => {
    console.log('图表点击:', params);
  };

  /**
   * 数据下探事件 - 通用下探处理
   */
  const onDrillDown = async (data: ChartDataItem, level: number, chartConfig?: any) => {
    console.log('数据下探:', data.name, level);

    try {
      // 使用传入的下探处理函数
      if (handleDrillDownFn) {
        await handleDrillDownFn(data, chartConfig);
      } else {
        message.warning('下探功能暂不可用');
      }
    } catch (error) {
      console.error('下探处理失败:', error);
      message.error('下探失败，请重试');
    }
  };

  /**
   * 数据点击事件
   */
  const onDataClick = (data: ChartDataItem) => {
    console.log('数据点击:', data.name);
  };

  /**
   * 图表双击事件
   */
  const onChartDblClick = (params: ChartEventParams) => {
    console.log('图表双击:', params);
  };

  /**
   * 图表区域点击事件
   */
  const onChartAreaClick = (event: any) => {
    console.log('图表区域点击:', event);
  };

  const eventContext: ChartEventContext = {
    onChartClick,
    onDrillDown,
    onDataClick,
    onChartDblClick,
    onChartAreaClick,
  };

  provide(CHART_EVENT_CONTEXT_KEY, eventContext);

  return eventContext;
}

/**
 * 提供图表状态上下文
 */
export function provideChartState(initialLoading = false) {
  const loading = ref(initialLoading);
  const chartLoadingStates = reactive<Record<string, boolean>>({});

  /**
   * 设置图表loading状态
   */
  const setChartLoading = (chartId: string, isLoading: boolean) => {
    chartLoadingStates[chartId] = isLoading;
  };

  const stateContext: ChartStateContext = {
    loading: loading.value,
    chartLoadingStates,
    setChartLoading,
  };

  provide(CHART_STATE_CONTEXT_KEY, stateContext);

  return {
    stateContext,
    loading,
    chartLoadingStates,
    setChartLoading,
  };
}

/**
 * 提供图表配置上下文
 */
export function provideChartConfig() {
  // 使用新的配置管理器
  const tabConfigManager = useTabConfigManager();
  const currentTabId = ref<string>('');

  /**
   * 获取图表配置
   */
  const getChartConfig = (chartId: string): ChartConfig | undefined => {
    return tabConfigManager.getChartConfig(chartId);
  };

  /**
   * 获取所有Tab配置
   */
  const getAllTabs = (): TabConfig[] => {
    return tabConfigManager.getAllTabs();
  };

  /**
   * 获取当前激活的Tab
   */
  const getCurrentTab = (): TabConfig | undefined => {
    if (!currentTabId.value) return undefined;
    return tabConfigManager.getTabConfig(currentTabId.value);
  };

  /**
   * 设置当前激活的Tab
   */
  const setCurrentTab = (tabId: string) => {
    currentTabId.value = tabId;
  };

  /**
   * 获取图表下探状态
   */
  const getChartDrillDownState = (chartId: string) => {
    const config = getChartConfig(chartId);
    if (!config?.drillDown) return null;

    const currentLevel = config.drillDown.currentLevel || 0;
    const isInDrillDown = currentLevel > 0;

    return { currentLevel, isInDrillDown };
  };

  const configContext: ChartConfigContext = {
    getChartConfig,
    getAllTabs,
    getCurrentTab,
    setCurrentTab,
    getChartDrillDownState,
  };

  provide(CHART_CONFIG_CONTEXT_KEY, configContext);

  return {
    configContext,
    currentTabId,
  };
}

/**
 * 使用图表操作上下文（在子组件中使用）
 */
export function useChartActions() {
  const context = inject(CHART_ACTION_CONTEXT_KEY);

  if (!context) {
    throw new Error('useChartActions must be used within a component that provides ChartActionContext');
  }

  return context;
}

/**
 * 可选的图表操作Hook（提供默认实现）
 */
export function useChartActionsOptional() {
  const context = inject(CHART_ACTION_CONTEXT_KEY, null);

  // 如果没有提供上下文，返回默认实现
  if (!context) {
    return {
      switchChartDataSource: (_chartId: string, _newDataSource: string, newTitle: string) => {
        console.warn('ChartActionContext not provided, using default implementation');
        message.info(`切换到 ${newTitle} (默认实现)`);
      },
      refreshChart: (_chartId: string) => {
        console.warn('ChartActionContext not provided, using default implementation');
        message.info('刷新图表 (默认实现)');
      },
      exportChart: (_chartId: string, _format: string) => {
        console.warn('ChartActionContext not provided, using default implementation');
        message.info(`导出图表 (默认实现)`);
      },
      fullscreenChart: (_chartId: string) => {
        console.warn('ChartActionContext not provided, using default implementation');
        message.info('全屏显示 (默认实现)');
      },
      handleDrillDown: async (_data: ChartDataItem, _chartConfig: any) => {
        console.warn('ChartActionContext not provided, using default implementation');
        message.info('下探功能 (默认实现)');
      },
      resetChartToTopLevel: async (_chartConfig: any) => {
        console.warn('ChartActionContext not provided, using default implementation');
        message.info('返回顶层 (默认实现)');
      },
    };
  }

  return context;
}

/**
 * 使用图表数据上下文
 */
export function useChartData() {
  const context = inject(CHART_DATA_CONTEXT_KEY);

  if (!context) {
    throw new Error('useChartData must be used within a component that provides ChartDataContext');
  }

  return context;
}

/**
 * 可选的图表数据Hook
 */
export function useChartDataOptional() {
  const context = inject(CHART_DATA_CONTEXT_KEY, null);

  if (!context) {
    return {
      getChartData: (_dataSource: string) => [],
      setChartData: (_dataSource: string, _data: ChartDataItem[]) => {},
      refreshChartData: async (_chartId: string) => {},
      clearDataCache: () => {},
    };
  }

  return context;
}

/**
 * 使用图表事件上下文
 */
export function useChartEvents() {
  const context = inject(CHART_EVENT_CONTEXT_KEY);

  if (!context) {
    throw new Error('useChartEvents must be used within a component that provides ChartEventContext');
  }

  return context;
}

/**
 * 可选的图表事件Hook
 */
export function useChartEventsOptional() {
  const context = inject(CHART_EVENT_CONTEXT_KEY, null);

  if (!context) {
    return {
      onChartClick: (_params: ChartEventParams) => {},
      onDrillDown: async (_data: ChartDataItem, _level: number, _chartConfig?: any) => {},
      onDataClick: (_data: ChartDataItem) => {},
      onChartDblClick: (_params: ChartEventParams) => {},
      onChartAreaClick: (_event: any) => {},
    };
  }

  return context;
}

/**
 * 使用图表状态上下文
 */
export function useChartState() {
  const context = inject(CHART_STATE_CONTEXT_KEY);

  if (!context) {
    throw new Error('useChartState must be used within a component that provides ChartStateContext');
  }

  return context;
}

/**
 * 可选的图表状态Hook
 */
export function useChartStateOptional() {
  const context = inject(CHART_STATE_CONTEXT_KEY, null);

  if (!context) {
    return {
      loading: false,
      chartLoadingStates: {},
      setChartLoading: (_chartId: string, _loading: boolean) => {},
      chartUpdateTimes: {},
      setChartUpdateTime: (_chartId: string, _time: Date) => {},
    };
  }

  return context;
}

/**
 * 使用图表配置上下文
 */
export function useChartConfig() {
  const context = inject(CHART_CONFIG_CONTEXT_KEY);

  if (!context) {
    throw new Error('useChartConfig must be used within a component that provides ChartConfigContext');
  }

  return context;
}

/**
 * 可选的图表配置Hook
 */
export function useChartConfigOptional() {
  const context = inject(CHART_CONFIG_CONTEXT_KEY, null);

  if (!context) {
    return {
      getChartConfig: (_chartId: string) => undefined,
      getAllTabs: () => [],
      getCurrentTab: () => undefined,
      setCurrentTab: (_tabId: string) => {},
      getChartDrillDownState: (_chartId: string) => null,
    };
  }

  return context;
}
