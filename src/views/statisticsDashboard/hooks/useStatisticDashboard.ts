/**
 * 统计看板状态管理 Hook
 * 简化版本，主要负责业务逻辑协调
 */

import { computed, onMounted, reactive, readonly } from 'vue';
import type { DashboardState } from '../types/statisticDashboard';
import { useTabConfigManager } from './useTabConfigManager';

/**
 * 统计看板状态管理
 */
export function useStatisticDashboard() {
  // 使用Tab配置管理器（包含Tab状态）
  const tabConfigManager = useTabConfigManager();

  console.log('tabs', tabConfigManager.availableTabs.value);

  // 计算属性：看板整体状态
  const dashboardState = computed<DashboardState>(() => ({
    tabs: tabConfigManager.availableTabs.value,
    activeTab: tabConfigManager.activeTabId.value,
    isDragging: false, // 拖拽功能暂时移除
    draggedItem: null,
    isExporting: false, // 导出功能暂时移除
  }));

  // 计算属性：是否有数据
  const hasData = computed(() => {
    return tabConfigManager.activeTabCharts.value.length > 0;
  });

  // 计算属性：加载状态
  const isLoading = computed(() => {
    // 这里可以根据实际需要添加加载状态逻辑
    return false;
  });

  /**
   * 切换Tab
   * @param tabId Tab ID
   */
  const switchTab = (tabId: string) => {
    return tabConfigManager.setActiveTab(tabId);
  };

  /**
   * 刷新当前Tab数据
   */
  const refreshCurrentTab = async () => {
    const currentTab = tabConfigManager.activeTab.value;
    if (currentTab) {
      // 数据刷新逻辑已移至专门的数据管理器
      console.log(`刷新Tab: ${currentTab.name}`);
    }
  };

  // 初始化默认激活Tab
  onMounted(() => {
    tabConfigManager.initializeActiveTab();
  });

  return {
    // 状态
    dashboardState,
    hasData,
    isLoading,

    // 直接暴露Tab管理器的状态和方法
    tabs: tabConfigManager.availableTabs,
    activeTab: tabConfigManager.activeTabId, // 注意：这里返回activeTabId而不是activeTab对象
    currentCharts: tabConfigManager.activeTabCharts,

    // 方法
    switchTab,
    refreshCurrentTab,
  };
}

/**
 * 看板布局管理
 */
export function useDashboardLayout() {
  // 自适应布局配置
  const layoutConfig = reactive({
    columns: 2, // 参考列数（不再固定使用）
    gap: 16, // 间距
    minChartWidth: 400, // 最小图表宽度
    maxChartWidth: 600, // 单列时的最大图表宽度
    singleColumnMaxWidth: 650, // 单列布局时的最大宽度
  });

  /**
   * 获取图表容器样式
   */
  const getChartContainerStyle = computed(() => ({
    display: 'grid',
    gridTemplateColumns: `repeat(auto-fit, minmax(${layoutConfig.minChartWidth}px, 1fr))`,
    gap: `${layoutConfig.gap}px`,
    padding: `${layoutConfig.gap}px`,
    maxWidth: '100%',
  }));

  // 简化的调整布局函数（保持接口兼容性）
  const adjustLayout = () => {
    // 不再进行响应式调整，保持固定布局
  };

  return {
    layoutConfig: readonly(layoutConfig),
    adjustLayout,
    getChartContainerStyle,
  };
}
