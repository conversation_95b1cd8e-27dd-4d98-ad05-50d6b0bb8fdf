/**
 * 图表数据管理 Hook
 * 提供图表数据的获取、缓存和更新功能
 */

import { reactive, computed, readonly } from 'vue';
import type { ChartDataItem } from '../types/statisticDashboard';
import { useDataSourceManager } from './useDataSourceManager';

/**
 * 图表数据管理Hook
 */
export function useChartData() {
  // 使用数据源管理器
  const dataSourceManager = useDataSourceManager();

  // 数据缓存
  const dataCache = reactive<Record<string, ChartDataItem[]>>({});

  // 加载状态
  const loadingStates = reactive<Record<string, boolean>>({});

  // 错误状态
  const errorStates = reactive<Record<string, string | null>>({});

  /**
   * 获取图表数据
   * @param dataSource 数据源标识
   * @returns 图表数据数组
   */
  const fetchChartData = async (dataSource: string): Promise<ChartDataItem[]> => {
    // 如果缓存中有数据，直接返回
    if (dataCache[dataSource]) {
      return dataCache[dataSource];
    }

    // 设置加载状态
    loadingStates[dataSource] = true;
    errorStates[dataSource] = null;

    try {
      // 模拟异步数据获取
      await new Promise((resolve) => setTimeout(resolve, 300));

      // 使用数据源管理器获取数据
      const data = dataSourceManager.getDataSource(dataSource);

      // 缓存数据
      dataCache[dataSource] = data;

      return data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取数据失败';
      errorStates[dataSource] = errorMessage;
      throw error;
    } finally {
      loadingStates[dataSource] = false;
    }
  };

  /**
   * 刷新图表数据
   * @param dataSource 数据源标识
   */
  const refreshChartData = async (dataSource: string): Promise<ChartDataItem[]> => {
    // 清除缓存
    delete dataCache[dataSource];

    // 重新获取数据
    return await fetchChartData(dataSource);
  };

  /**
   * 批量获取图表数据
   * @param dataSources 数据源标识数组
   */
  const fetchMultipleChartData = async (dataSources: string[]): Promise<Record<string, ChartDataItem[]>> => {
    const promises = dataSources.map(async (dataSource) => {
      const data = await fetchChartData(dataSource);
      return { dataSource, data };
    });

    const results = await Promise.all(promises);

    return results.reduce(
      (acc, { dataSource, data }) => {
        acc[dataSource] = data;
        return acc;
      },
      {} as Record<string, ChartDataItem[]>
    );
  };

  /**
   * 清除数据缓存
   * @param dataSource 可选，指定清除的数据源，不传则清除所有
   */
  const clearCache = (dataSource?: string) => {
    if (dataSource) {
      delete dataCache[dataSource];
      delete loadingStates[dataSource];
      delete errorStates[dataSource];
    } else {
      Object.keys(dataCache).forEach((key) => {
        delete dataCache[key];
        delete loadingStates[key];
        delete errorStates[key];
      });
    }
  };

  /**
   * 获取数据加载状态
   * @param dataSource 数据源标识
   */
  const isLoading = (dataSource: string): boolean => {
    return loadingStates[dataSource] || false;
  };

  /**
   * 获取数据错误状态
   * @param dataSource 数据源标识
   */
  const getError = (dataSource: string): string | null => {
    return errorStates[dataSource] || null;
  };

  /**
   * 检查数据是否已缓存
   * @param dataSource 数据源标识
   */
  const isCached = (dataSource: string): boolean => {
    return !!dataCache[dataSource];
  };

  /**
   * 获取缓存的数据
   * @param dataSource 数据源标识
   */
  const getCachedData = (dataSource: string): ChartDataItem[] | null => {
    return dataCache[dataSource] || null;
  };

  /**
   * 预加载数据
   * @param dataSources 需要预加载的数据源数组
   */
  const preloadData = async (dataSources: string[]): Promise<void> => {
    const uncachedSources = dataSources.filter((source) => !isCached(source));

    if (uncachedSources.length > 0) {
      await fetchMultipleChartData(uncachedSources);
    }
  };

  // 计算属性：所有加载状态
  const isAnyLoading = computed(() => {
    return Object.values(loadingStates).some((loading) => loading);
  });

  // 计算属性：所有错误状态
  const hasAnyError = computed(() => {
    return Object.values(errorStates).some((error) => error !== null);
  });

  // 计算属性：缓存统计
  const cacheStats = computed(() => {
    const totalCached = Object.keys(dataCache).length;
    const totalLoading = Object.values(loadingStates).filter(Boolean).length;
    const totalErrors = Object.values(errorStates).filter(Boolean).length;

    return {
      totalCached,
      totalLoading,
      totalErrors,
    };
  });

  return {
    // 数据获取方法
    fetchChartData,
    refreshChartData,
    fetchMultipleChartData,
    preloadData,

    // 缓存管理
    clearCache,
    getCachedData,
    isCached,

    // 状态查询
    isLoading,
    getError,
    isAnyLoading,
    hasAnyError,
    cacheStats,

    // 响应式数据（只读）
    dataCache: readonly(dataCache),
    loadingStates: readonly(loadingStates),
    errorStates: readonly(errorStates),
  };
}
