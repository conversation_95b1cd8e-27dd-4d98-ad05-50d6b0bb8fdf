<template>
  <a-card>
    <BasicForm @register="registerForm" @submit="handleSubmit" @cancel="handleCancel" @field-value-change="fieldValueChange">
      <template #roleButton>
        <a-button class="select_role" type="primary" @click="openSelectTemplateModal(true, tableData)">{{ t('selectRole') }}</a-button>
      </template>
    </BasicForm>

    <SelectTemplateModal
      :selected-row-keys="selectedTemplateRowKeys"
      @register="registerSelectTemplateModal"
      @confirm="handleSelectTemplateConfirm"
    />
  </a-card>
</template>
<script setup lang="ts">
  import { onMounted, ref, toRaw } from 'vue';
  import { router } from '/@/router';
  import { useSessionStorageState } from 'vue-hooks-plus';
  import { awaitTo } from '@ruqi/utils-admin/esm/tools';
  import { useForm, BasicForm, FormSchema } from '/@/components/Form';
  import { useModal } from '/@/components/Modal';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useRoutePageType } from '/@/hooks/route';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { PageTypeEnum } from '/@/enums/common';
  import { useTabs } from '/@/hooks/web/useTabs';
  import { useBSideUserNetworkOptions, useUserRegionOptions, useStoreRoleOptions } from '/@/hooks/common';
  import { SelectOptionType } from '/@/hooks/common/useRegionOptions';
  import { featchStoreUserAdd, featchStoreUserEdit, StoreUserType } from '/@/api/sys/store';
  import { getNetworkAllDataList } from '/@/api/basicData/network';
  import { useUserStore } from '/@/store/modules/user';
  import SelectTemplateModal from './SelectTemplateModal.vue';

  const t = useI18n('common').t;
  const { userInfo } = useUserStore();
  // 选择角色弹窗
  const [registerSelectTemplateModal, { openModal: openSelectTemplateModal }] = useModal();
  const selectedTemplateRowKeys = ref<number[]>([]); // 选中的模板行key
  const selectedTemplateRows = ref<any[]>([]); // 选中的模板行rows
  const tableData = ref<any[]>([]); // 表格数据

  const nationOptions = ref<SelectOptionType[]>([]);
  const { regionOptions, getData: getRegionData } = useUserRegionOptions();
  const { roleOptions, networkData, bSideData, allData, getData: getRoleData } = useStoreRoleOptions();
  const { getData, bSideUserOptions } = useBSideUserNetworkOptions();
  const networkOptions = ref<SelectOptionType[]>([]);
  const roleTypes = ref<number[]>([]);
  // 总代子公司 2  网点 3
  const CUSTOMER_CODE_ROLE = 2;
  const NETWORK_CODE_ROLE = 3;

  const pageType = useRoutePageType();
  const [storeInfo] = useSessionStorageState('StoreAccountItem', { defaultValue: {} as StoreUserType });

  const id = Number(storeInfo.value?.id);

  const formSchemas: FormSchema[] = [
    {
      label: t('regionText'),
      field: 'regionCode',
      component: 'Select',
      labelWidth: '100%',
      required: true,
      componentProps: {
        placeholder: t('chooseText'),
        options: regionOptions,
        showSearch: true,
        onChange: (_, opt) => {
          setFieldsValue({
            countryCode: null,
            networkCode: [],
            role: [],
            customerCode: [],
          });
          resetRoleAndOptions();
          nationOptions.value = opt?.children || [];
          const countryValue = opt?.children?.length === 1 ? opt?.children[0]?.value : '';
          if (countryValue) {
            changeCountryCode(countryValue, true);
          }
        },
      },
    },
    {
      label: t('countryText'),
      field: 'countryCode',
      component: 'Select',
      labelWidth: '100%',
      required: true,
      componentProps: {
        placeholder: t('chooseText'),
        options: nationOptions,
        showSearch: true,
      },
    },
    {
      label: t('dealerCustomerText'),
      field: 'customerCode',
      component: 'Select',
      labelWidth: '100%',
      required: true,
      componentProps: {
        placeholder: t('chooseText'),
        options: bSideUserOptions,
        showSearch: true,
        mode: 'multiple',
      },
    },
    {
      label: t('networkText'),
      field: 'networkCode',
      component: 'Select',
      labelWidth: '100%',
      componentProps: {
        placeholder: t('chooseText'),
        options: networkOptions,
        showSearch: true,
        mode: 'multiple',
      },
    },
    {
      label: t('role_text'),
      field: 'role',
      component: 'Select',
      labelWidth: '100%',
      required: true,
      componentProps: {
        placeholder: t('chooseText'),
        options: roleOptions,
        showSearch: true,
        mode: 'multiple',
        disabled: true,
      },
    },
    {
      label: '',
      field: 'roleButtonSelect',
      slot: 'roleButton',
      component: 'Input',
    },
  ];

  const submitButtonOptions = ref({ text: t('saveText'), preIcon: '', loading: false });

  const { close: closeTab } = useTabs();

  const [registerForm, { setFieldsValue, getFieldsValue }] = useForm({
    schemas: formSchemas,
    autoSubmitOnEnter: true,
    showResetButton: false,
    showCancelButton: true,
    cancelButtonOptions: { text: t('cancelText'), preIcon: '' },
    showSubmitButton: true,
    submitButtonOptions,
    buttonLayout: 'left',
    labelAlign: 'left',
    layout: 'vertical',
  });

  /** 提交按钮配置 */
  const apiMap = {
    [PageTypeEnum.ADD]: featchStoreUserAdd,
    [PageTypeEnum.EDIT]: featchStoreUserEdit,
  };

  const { createMessage } = useMessage();

  const handleSubmit = async (values: Record<string, any>) => {
    if (!values.role) {
      createMessage.error(t('pleaseSelectSameRole'));
      return;
    }
    const api = apiMap[pageType];
    const params = values;
    if (pageType === PageTypeEnum.EDIT) {
      params.id = id;
    }
    submitButtonOptions.value.loading = true;
    const [err] = await awaitTo(api(params));
    if (!err) {
      createMessage.success(t('operationSuccess'));
      handleCancel();
    }
    submitButtonOptions.value.loading = false;
  };

  const handleCancel = () => {
    closeTab();
    router.push('/system/store-account');
  };

  const handleSelectTemplateConfirm = (_selectedRows: any[], _selectedRowKeys: number[]) => {
    selectedTemplateRows.value = _selectedRows;
    selectedTemplateRowKeys.value = _selectedRowKeys;
    setFieldsValue({ role: _selectedRowKeys });
  };
  // 获取其网点列表
  const getNetworkList = async (value: string[]) => {
    const [, res] = await awaitTo(getNetworkAllDataList({ customerCode: value }));
    if (res) {
      networkOptions.value = (res?.map((item) => ({ label: item.networkName, value: item.networkCode })) as SelectOptionType[]) || [];
    }
  };

  // 判断是否有默认角色
  const setDefaultRole = () => {
    if (selectedTemplateRowKeys.value.length > 0) return;
    // 这里角色标签可能会有多个用;号隔开，比如 dealer_admin;test ....
    const roleDefault = tableData.value.find((item) => item.roleSign?.includes('dealer_admin'));
    if (roleDefault) {
      selectedTemplateRows.value.push(roleDefault);
      selectedTemplateRowKeys.value = selectedTemplateRows.value.map((item) => item.roleId);
      setFieldsValue({ role: selectedTemplateRowKeys.value });
    }
  };

  // 更新角色列表
  const updateTableData = () => {
    tableData.value = allData.value.filter((item) => roleTypes.value.includes(item.authorizeLevel)).map((item) => ({ ...item }));
    setDefaultRole();
  };

  // 清空网点角色数据
  const resetRoleAndOptions = () => {
    networkOptions.value = [];
    selectedTemplateRows.value = [];
    selectedTemplateRowKeys.value = [];
    roleTypes.value = [];
  };

  const changeCountryCode = async (value: string | number, isSet = false) => {
    const updataData: Record<string, unknown> = {};
    if (isSet) {
      updataData.countryCode = value;
    }
    if (value) {
      await getData({ countryCode: value });
      const setValue: string[] = bSideUserOptions.value.length === 1 ? [String(bSideUserOptions.value[0].value)] : [];
      await changeCustomerCode(setValue, true);
    } else {
      resetRoleAndOptions();
      updataData.networkCode = [];
      updataData.role = [];
      updataData.customerCode = [];
    }
    setFieldsValue({ ...updataData });
  };

  const changeCustomerCode = async (value: string[], isSet = false) => {
    const updataData: Record<string, unknown> = {};
    if (isSet) {
      updataData.customerCode = value;
    }
    if (value?.length) {
      if (!roleTypes.value.includes(CUSTOMER_CODE_ROLE)) {
        roleTypes.value.push(CUSTOMER_CODE_ROLE);
      }
      await getNetworkList(value);
      const formValue = getFieldsValue();
      if (formValue?.networkCode) {
        const plainArray = toRaw(networkOptions.value);
        const codes = (formValue.networkCode as string).split(',');
        const result = plainArray.filter((item) => codes.includes(String(item.value)));
        let codeIds = result.map((item) => item.value as string);
        if (!codeIds.length && networkOptions.value.length === 1) {
          codeIds = [String(networkOptions.value[0].value)];
        }
        await changeNetworkCode(codeIds, true);
      } else if (networkOptions.value.length === 1) {
        await changeNetworkCode([String(networkOptions.value[0].value)], true);
      }
    } else {
      resetRoleAndOptions();
      updataData.networkCode = [];
      updataData.role = [];
    }
    setFieldsValue({ ...updataData });
    updateTableData();
  };

  const changeNetworkCode = async (value: string[], isSet = false) => {
    const updataData: Record<string, unknown> = {};
    if (isSet) {
      updataData.networkCode = value;
    }
    if (value?.length) {
      if (!roleTypes.value.includes(NETWORK_CODE_ROLE)) {
        roleTypes.value.push(NETWORK_CODE_ROLE);
      }
    } else {
      roleTypes.value = roleTypes.value.filter((item) => item !== NETWORK_CODE_ROLE);
      selectedTemplateRows.value = selectedTemplateRows.value.filter((item) => item.authorizeLevel !== NETWORK_CODE_ROLE);
      selectedTemplateRowKeys.value = selectedTemplateRows.value.map((item) => item.roleId);
      updataData.role = selectedTemplateRowKeys.value;
    }
    setFieldsValue({ ...updataData });
    updateTableData();
  };

  const fieldValueChange = async (field: string, value: unknown) => {
    switch (field) {
      case 'countryCode':
        await changeCountryCode(value as string);
        break;
      case 'customerCode':
        await changeCustomerCode(value as string[]);
        break;
      case 'networkCode':
        await changeNetworkCode(value as string[]);
        break;
    }
  };
  onMounted(async () => {
    await awaitTo(Promise.all([getRegionData(userInfo?.userId), getRoleData()]));
    if (pageType === PageTypeEnum.ADD && regionOptions.value?.length === 1) {
      const obj: any = {
        regionCode: regionOptions.value[0].value,
      };
      nationOptions.value = regionOptions.value[0].children || [];
      if (nationOptions.value?.length === 1) {
        changeCountryCode(nationOptions.value[0].value, true);
      }
      setFieldsValue({ ...obj });
    }
    if (pageType !== PageTypeEnum.ADD) {
      const roleList = storeInfo.value?.roleId?.split(',').map(Number) || [];
      const obj = {
        regionCode: storeInfo.value?.regionCode || null,
        countryCode: storeInfo.value?.countryCode || null,
        customerCode: storeInfo.value?.customerCode?.split(',') || [],
        networkCode: (storeInfo.value?.networkCode || '').split(',').filter((code) => code.trim() !== ''),
        role: roleList,
      };
      if (obj?.regionCode) {
        nationOptions.value = regionOptions.value.filter((item) => item.value === obj.regionCode)[0]?.children || [];
      }
      if (obj?.countryCode) {
        await getData({ countryCode: obj.countryCode });
      }
      if (storeInfo.value?.customerCode) {
        roleTypes.value.push(2);
        await getNetworkList(obj.customerCode);
      }
      if (storeInfo.value?.networkCode) {
        roleTypes.value.push(3);
      }
      updateTableData();
      setFieldsValue(obj);
      selectedTemplateRowKeys.value = roleList;
      selectedTemplateRows.value = [...bSideData.value, ...networkData.value].filter((item) => roleList.includes(item.id));
    }
  });
</script>

<style lang="less" scoped>
  ::v-deep(.ant-card-head) {
    border-bottom: none;
  }
  .select_role {
    position: absolute;
    right: 0;
    bottom: 56px;
    z-index: 99;
  }
</style>
