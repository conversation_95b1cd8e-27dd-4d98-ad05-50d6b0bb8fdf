/**
 * CDN 动态加载工具
 * 用于按需加载低频使用的第三方库
 */

interface CDNResource {
  name: string;
  url: string;
  globalName?: string;
  css?: string[];
  additionalScripts?: string[]; // 额外需要加载的脚本
  workerUrl?: string; // Worker 脚本URL
  fallbackUrls?: string[]; // 备用CDN地址
}

// CDN 资源配置
export const CDN_RESOURCES: Record<string, CDNResource> = {
  // word文档预览 h5页面有使用
  mammoth: {
    name: 'mammoth',
    url: 'https://cdn.bootcdn.net/ajax/libs/mammoth/1.9.0/mammoth.browser.min.js',
    globalName: 'mammoth',
  },
  // pdf预览 pc 页面有使用
  'pdfjs-dist': {
    name: 'pdfjs-dist',
    url: 'https://cdn.bootcdn.net/ajax/libs/pdf.js/5.1.91/pdf.min.mjs',
    globalName: 'pdfjsLib',
    workerUrl: 'https://cdn.bootcdn.net/ajax/libs/pdf.js/5.1.91/pdf.worker.min.mjs',
  },
  // 暂无使用,预留
  vditor: {
    name: 'vditor',
    url: 'https://unpkg.com/vditor@3.10.1/dist/index.min.js',
    globalName: 'Vditor',
    css: ['https://unpkg.com/vditor@3.10.1/dist/index.css'],
  },
  // Iconify 图标库 - 替代 @purge-icons/generated
  iconify: {
    name: 'iconify',
    url: 'https://cdn.jsdelivr.net/npm/iconify-icon@2.1.0/dist/iconify-icon.min.js',
    globalName: 'IconifyIcon',
    fallbackUrls: [
      'https://unpkg.com/iconify-icon@2.1.0/dist/iconify-icon.min.js',
      'https://cdn.skypack.dev/iconify-icon@2.1.0/dist/iconify-icon.min.js',
    ],
  },
};

// 已加载的资源缓存
const loadedResources = new Set<string>();
const loadingPromises = new Map<string, Promise<any>>();

/**
 * 动态加载 CDN 资源
 * @param resourceName 资源名称
 * @returns Promise<any> 返回加载的库对象
 */
export async function loadCDNResource(resourceName: string): Promise<any> {
  const resource = CDN_RESOURCES[resourceName];

  if (!resource) {
    throw new Error(`Unknown CDN resource: ${resourceName}`);
  }

  // 如果已经加载过，直接返回
  if (loadedResources.has(resourceName)) {
    return getGlobalLibrary(resource);
  }

  // 如果正在加载中，返回加载中的 Promise
  if (loadingPromises.has(resourceName)) {
    return loadingPromises.get(resourceName);
  }

  // 开始加载
  const loadPromise = loadResource(resource);
  loadingPromises.set(resourceName, loadPromise);

  try {
    const result = await loadPromise;
    loadedResources.add(resourceName);
    loadingPromises.delete(resourceName);
    return result;
  } catch (error) {
    loadingPromises.delete(resourceName);
    throw error;
  }
}

/**
 * 加载单个资源
 */
async function loadResource(resource: CDNResource): Promise<any> {
  // 加载 CSS 文件（如果有）
  if (resource.css) {
    await Promise.all(resource.css.map(loadCSS));
  }

  // 尝试加载 JavaScript 文件，支持备用 URL
  const urlsToTry = [resource.url, ...(resource.fallbackUrls || [])];
  let lib: any;
  let lastError: any;

  for (const url of urlsToTry) {
    try {
      if (url.endsWith('.mjs')) {
        // ES Module
        lib = await import(/* @vite-ignore */ url);
      } else {
        // UMD/Global script
        await loadScript(url);
        lib = getGlobalLibrary(resource);
      }
      console.log(`Successfully loaded ${resource.name} from ${url}`);
      break;
    } catch (error) {
      console.warn(`Failed to load ${resource.name} from ${url}:`, error);
      lastError = error;
      continue;
    }
  }

  if (!lib) {
    throw new Error(`Failed to load ${resource.name} from all URLs. Last error: ${lastError}`);
  }

  return lib;
}

/**
 * 加载 CSS 文件
 */
function loadCSS(url: string): Promise<void> {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载
    const existingLink = document.querySelector(`link[href="${url}"]`);
    if (existingLink) {
      resolve();
      return;
    }

    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = url;
    link.onload = () => resolve();
    link.onerror = () => reject(new Error(`Failed to load CSS: ${url}`));
    document.head.appendChild(link);
  });
}

/**
 * 加载 JavaScript 文件
 */
function loadScript(url: string): Promise<void> {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载
    const existingScript = document.querySelector(`script[src="${url}"]`);
    if (existingScript) {
      resolve();
      return;
    }

    const script = document.createElement('script');
    script.src = url;
    script.onload = () => resolve();
    script.onerror = () => reject(new Error(`Failed to load script: ${url}`));
    document.head.appendChild(script);
  });
}

/**
 * 获取全局库对象
 */
function getGlobalLibrary(resource: CDNResource): any {
  if (!resource.globalName) {
    throw new Error(`No global name specified for resource: ${resource.name}`);
  }

  const lib = (window as any)[resource.globalName];
  if (!lib) {
    throw new Error(`Global library not found: ${resource.globalName}`);
  }

  return lib;
}

/**
 * 预加载资源（可选）
 * @param resourceNames 要预加载的资源名称数组
 */
export async function preloadCDNResources(resourceNames: string[]): Promise<void> {
  await Promise.all(resourceNames.map(loadCDNResource));
}

/**
 * 检查资源是否已加载
 * @param resourceName 资源名称
 * @returns boolean
 */
export function isCDNResourceLoaded(resourceName: string): boolean {
  return loadedResources.has(resourceName);
}

/**
 * 获取资源的 Worker URL
 * @param resourceName 资源名称
 * @returns string | undefined
 */
export function getCDNResourceWorkerUrl(resourceName: string): string | undefined {
  const resource = CDN_RESOURCES[resourceName];
  return resource?.workerUrl;
}

// 导出具体的加载函数，方便使用
export const loadMammoth = () => loadCDNResource('mammoth');
export const loadPDFJS = () => loadCDNResource('pdfjs-dist');
export const loadVditor = () => loadCDNResource('vditor');
export const loadIconify = () => loadCDNResource('iconify');
