# SWC 压缩配置经验总结

## ⚠️ 重要提示

**SWC 压缩插件已从项目中移除**，原因是在生产环境中出现运行时异常。本文档记录了 SWC 配置的经验教训，供后续参考。

## 问题背景

### 初始目标
- 提升压缩效果，超越 esbuild 的 15-25% 压缩率
- 减少生产环境文件大小，提升用户体验
- 探索现代构建工具的优化潜力

### 实施过程
1. **第一阶段**: 保守配置，压缩率 50-65%
2. **第二阶段**: 积极配置，压缩率 60-75%
3. **问题出现**: 运行时异常 `Cannot set properties of undefined (setting 'placeholder')`
4. **最终决策**: 移除 SWC，回退到 esbuild

## 技术问题分析

### 运行时异常根因
```
Uncaught TypeError: Cannot set properties of undefined (setting 'placeholder')
```

**根本原因**: SWC 的属性名混淆破坏了 Ant Design Vue 的属性访问

#### 问题配置
```typescript
mangle: {
  props: {
    reserved: [/* 保留属性列表 */]
  }
}
```

#### 技术原理
1. **属性名混淆**: SWC 将 `placeholder` 等属性名重命名为短名称
2. **框架依赖**: Ant Design Vue 通过字符串访问属性，如 `obj['placeholder']`
3. **访问失败**: 混淆后的属性名导致访问失败，返回 undefined
4. **运行时错误**: 尝试设置 undefined 的属性导致异常

### 其他危险配置
```typescript
compress: {
  expression: true,        // 表达式优化可能破坏框架逻辑
  properties: true,        // 属性访问优化影响动态属性
  reduce_vars: true,       // 变量简化可能影响作用域
  side_effects: true,      // 副作用移除可能删除必要代码
  keep_fnames: false,      // 函数名混淆影响反射机制
  keep_classnames: false,  // 类名混淆影响框架识别
}
```

## 经验教训

### 失败原因分析

#### 1. 过度优化
- **问题**: 启用了过多激进的压缩选项
- **影响**: 破坏了框架的内部机制
- **教训**: 压缩效果不应以牺牲稳定性为代价

#### 2. 测试不充分
- **问题**: 本地测试通过，但生产环境失败
- **影响**: 部署后才发现问题，影响用户体验
- **教训**: 需要在多种环境下进行充分测试

#### 3. 框架兼容性忽视
- **问题**: 低估了现代框架对属性名的依赖
- **影响**: 属性名混淆导致框架功能异常
- **教训**: 对于复杂框架项目，应保持保守的压缩策略

#### 4. 配置复杂性
- **问题**: SWC 配置选项众多，相互影响复杂
- **影响**: 难以精确控制压缩行为
- **教训**: 简单可靠的方案往往比复杂方案更好

### 成功经验

#### 1. 渐进式优化
- 从保守配置开始，逐步启用更多选项
- 每次调整后都进行完整测试
- 出现问题时能快速定位和回退

#### 2. 性能监控
- 建立了完整的构建性能监控体系
- 能够量化优化效果
- 为决策提供数据支持

#### 3. 文档记录
- 详细记录了每次配置调整
- 保留了问题排查过程
- 为后续优化提供参考

## 替代方案对比

### esbuild vs SWC

| 特性 | esbuild | SWC |
|------|---------|-----|
| **压缩率** | 15-25% | 35-50% |
| **稳定性** | ✅ 高 | ⚠️ 中等 |
| **配置复杂度** | ✅ 简单 | ❌ 复杂 |
| **框架兼容性** | ✅ 优秀 | ⚠️ 需谨慎 |
| **构建速度** | ✅ 快 | ✅ 快 |
| **社区支持** | ✅ 成熟 | ✅ 活跃 |
| **生产就绪** | ✅ 是 | ⚠️ 需验证 |

### 最终选择: esbuild
**理由**:
- 稳定性和可靠性是生产环境的首要考虑
- 15-25% 的压缩率已经能满足大部分需求
- 配置简单，维护成本低
- 与 Vite 深度集成，兼容性好

## 后续优化建议

### 短期方案
1. **保持 esbuild 压缩**: 稳定可靠，满足当前需求
2. **优化分包策略**: 通过更好的代码分割提升性能
3. **资源优化**: 图片、字体等静态资源的优化
4. **缓存策略**: 利用浏览器缓存提升加载速度

### 长期方案
1. **关注 SWC 发展**: 等待更成熟的框架兼容性
2. **探索其他工具**: 如 Terser、Rollup 等压缩工具
3. **代码重构**: 减少对动态属性访问的依赖
4. **微前端架构**: 通过架构优化减少单体应用的复杂性

## 配置保留

### 安全的 SWC 配置（仅供参考）
```typescript
// 如果将来需要重新尝试 SWC，可以参考这个保守配置
export function createSwcMinifyPlugin(): Plugin {
  return {
    name: 'vite:swc-minify',
    apply: 'build',
    enforce: 'post',
    
    async generateBundle(options, bundle) {
      for (const [fileName, chunk] of Object.entries(bundle)) {
        if (chunk.type === 'chunk' && fileName.endsWith('.js')) {
          const result = await transform(chunk.code, {
            jsc: {
              target: 'es2018',
              minify: {
                compress: {
                  // 只启用最安全的选项
                  dead_code: true,
                  drop_console: true,
                  drop_debugger: true,
                  booleans: true,
                  comparisons: true,
                  conditionals: true,
                  // 禁用所有可能影响框架的选项
                  expression: false,
                  properties: false,
                  reduce_vars: false,
                  side_effects: false,
                },
                mangle: {
                  // 保持函数名和类名
                  keep_classnames: true,
                  keep_fnames: true,
                  // 不混淆属性名
                  reserved: [/* 大量保留名称 */]
                }
              }
            },
            minify: true
          })
          chunk.code = result.code
        }
      }
    }
  }
}
```

## 总结

SWC 压缩虽然能提供优秀的压缩效果，但在复杂的 Vue + Ant Design 项目中存在兼容性风险。对于生产环境，稳定性应该优先于压缩效果。esbuild 作为成熟的解决方案，能够在稳定性和性能之间提供良好的平衡。

**核心原则**: 在生产环境中，稳定可靠的方案永远比激进的优化更重要。
